import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import PerformancePage from '@/app/dashboard/performance/page'

// Mock the chart provider and components
vi.mock('@/lib/chart-provider', () => ({
  ChartProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="chart-provider">{children}</div>,
  RechartsComponents: ({ children }: { children: (components: any) => React.ReactNode }) => {
    const mockComponents = {
      ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-container">{children}</div>,
      BarChart: ({ children }: { children: React.ReactNode }) => <div data-testid="bar-chart">{children}</div>,
      CartesianGrid: () => <div data-testid="cartesian-grid" />,
      XAxis: () => <div data-testid="x-axis" />,
      YAxis: () => <div data-testid="y-axis" />,
      Tooltip: () => <div data-testid="tooltip" />,
      Bar: ({ children }: { children: React.ReactNode }) => <div data-testid="bar">{children}</div>,
      Cell: () => <div data-testid="cell" />
    }
    return <>{children(mockComponents)}</>
  }
}))

// Mock the lazy-loaded components
vi.mock('@/components/dashboard/performance/performance-trends', () => ({
  default: () => <div data-testid="performance-trends">Performance Trends</div>
}))

vi.mock('@/components/dashboard/performance/top-performing-content', () => ({
  default: () => <div data-testid="top-performing-content">Top Performing Content</div>
}))

vi.mock('@/components/dashboard/performance/performance-goals', () => ({
  default: () => <div data-testid="performance-goals">Performance Goals</div>
}))

vi.mock('@/components/dashboard/performance/platform-performance', () => ({
  default: () => <div data-testid="platform-performance">Platform Performance</div>
}))

vi.mock('@/components/dashboard/performance/performance-insights', () => ({
  default: () => <div data-testid="performance-insights">Performance Insights</div>
}))

// Mock the dashboard layout
vi.mock('@/components/dashboard/dashboard-layout', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="dashboard-layout">{children}</div>
}))

// Mock the streaming error boundary
vi.mock('@/components/ui/streaming-error-boundary', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div data-testid="error-boundary">{children}</div>
}))

describe('Performance Dashboard', () => {
  it('renders without crashing', async () => {
    render(<PerformancePage />)
    
    // Check that the main components are rendered
    expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
    expect(screen.getByTestId('dashboard-layout')).toBeInTheDocument()
  })

  it('loads performance components', async () => {
    render(<PerformancePage />)
    
    // Wait for lazy-loaded components to appear
    await waitFor(() => {
      expect(screen.getByTestId('performance-trends')).toBeInTheDocument()
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('top-performing-content')).toBeInTheDocument()
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('performance-goals')).toBeInTheDocument()
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('platform-performance')).toBeInTheDocument()
    })
    
    await waitFor(() => {
      expect(screen.getByTestId('performance-insights')).toBeInTheDocument()
    })
  })

  it('renders chart components correctly', async () => {
    render(<PerformancePage />)
    
    // Check that chart provider is working
    await waitFor(() => {
      expect(screen.getByTestId('chart-provider')).toBeInTheDocument()
    })
  })

  it('handles errors gracefully', async () => {
    // This test verifies that the error boundary is in place
    render(<PerformancePage />)
    
    expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
  })
})

describe('Performance Dashboard - Chart Integration', () => {
  it('chart provider provides necessary components', () => {
    const { ChartProvider, RechartsComponents } = require('@/lib/chart-provider')
    
    expect(ChartProvider).toBeDefined()
    expect(RechartsComponents).toBeDefined()
  })

  it('bar chart components are available', async () => {
    const { render } = await import('@testing-library/react')
    const { ChartProvider, RechartsComponents } = require('@/lib/chart-provider')
    
    render(
      <ChartProvider>
        <RechartsComponents>
          {(components: any) => (
            <div>
              <components.BarChart data-testid="test-bar-chart" />
              <components.ResponsiveContainer data-testid="test-responsive-container" />
            </div>
          )}
        </RechartsComponents>
      </ChartProvider>
    )
    
    expect(screen.getByTestId('test-bar-chart')).toBeInTheDocument()
    expect(screen.getByTestId('test-responsive-container')).toBeInTheDocument()
  })
})

describe('Performance Dashboard - Memory and Performance', () => {
  it('components are memoized', () => {
    // This test would verify that React.memo is applied
    // In a real test, you'd check that components don't re-render unnecessarily
    expect(true).toBe(true) // Placeholder for actual memoization test
  })

  it('lazy loading is implemented', async () => {
    // Verify that components are lazy-loaded
    const performanceTrends = await import('@/components/dashboard/performance/performance-trends')
    expect(performanceTrends.PerformanceTrends).toBeDefined()
  })
})

#!/bin/bash

# 🔧 Debug Integration Setup Script
# This script sets up comprehensive debugging tools for your Next.js project

echo "🔧 Setting up Debug Integration for Next.js Dashboard..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in a Next.js project
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from your Next.js project root."
    exit 1
fi

if ! grep -q "next" package.json; then
    print_error "This doesn't appear to be a Next.js project."
    exit 1
fi

print_status "Next.js project detected"

# Check if debug files already exist
if [ -d "lib/debug" ] && [ -d "components/debug" ]; then
    print_warning "Debug files already exist. Skipping file creation."
else
    print_info "Debug infrastructure files have been created"
fi

# Install additional dependencies if needed
print_info "Checking for required dependencies..."

# Check if we need to install any additional packages
MISSING_DEPS=()

# Check for required UI components (these should already be installed based on your package.json)
if ! npm list @radix-ui/react-collapsible >/dev/null 2>&1; then
    MISSING_DEPS+=("@radix-ui/react-collapsible")
fi

if ! npm list @radix-ui/react-scroll-area >/dev/null 2>&1; then
    MISSING_DEPS+=("@radix-ui/react-scroll-area")
fi

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    print_info "Installing missing dependencies: ${MISSING_DEPS[*]}"
    npm install "${MISSING_DEPS[@]}"
    print_status "Dependencies installed"
else
    print_status "All required dependencies are already installed"
fi

# Create .env.local if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "# Debug Configuration" > .env.local
    echo "NODE_ENV=development" >> .env.local
    print_status "Created .env.local with debug configuration"
else
    print_info ".env.local already exists"
fi

# Add debug scripts to package.json if they don't exist
print_info "Checking package.json scripts..."

if ! grep -q "dev:debug" package.json; then
    print_warning "Debug scripts not found in package.json. Please add them manually or re-run the integration."
else
    print_status "Debug scripts found in package.json"
fi

# Create a simple test file to verify debug setup
cat > debug-test.js << 'EOF'
// Simple test to verify debug setup
console.log('🔧 Debug Integration Test');

// Test if we can import debug modules
try {
    const { logger } = require('./lib/debug/logger');
    logger.info('Debug logger is working!');
    console.log('✅ Logger test passed');
} catch (error) {
    console.error('❌ Logger test failed:', error.message);
}

console.log('🎉 Debug integration test complete!');
EOF

print_status "Created debug test file"

# Create a README for debug tools
cat > DEBUG_README.md << 'EOF'
# 🔧 Debug Tools Guide

## Quick Start

1. **Start development with debugging:**
   ```bash
   npm run dev:debug
   ```

2. **Open your browser and look for:**
   - Debug panel in bottom-right corner (development only)
   - Console logs with emoji prefixes
   - Performance metrics on components

## Debug Features

### 🐛 Error Boundary
- Catches and displays React errors gracefully
- Shows error details in development
- Provides retry functionality

### 📊 Performance Monitoring
- Tracks component render times
- Warns about slow renders (>16ms)
- Shows render counts and averages

### 🌐 Network Debugging
- Logs all fetch requests and responses
- Tracks API performance metrics
- Shows success/error rates

### 📱 Component Debugging
- Tracks component lifecycle
- Monitors state changes
- Counts renders and instances

## Debug Panel

The debug panel appears in the bottom-right corner in development mode:

- **Logs Tab**: Recent debug messages
- **Performance Tab**: Component metrics
- **Network Tab**: API request statistics

## Debug Hooks

### useDebugHook
```typescript
import { useDebugHook } from '@/lib/debug/useDebugValue'

function MyComponent() {
  const [state, setState] = useState(0)
  useDebugHook(state, 'Component State', 'MyComponent')
  // Logs state changes automatically
}
```

### useComponentDebug
```typescript
import { useComponentDebug } from '@/lib/debug/component-debug'

function MyComponent(props) {
  useComponentDebug('MyComponent', props)
  // Tracks renders, props changes, lifecycle
}
```

### PerformanceWrapper
```typescript
import { PerformanceWrapper } from '@/components/debug/PerformanceWrapper'

function MyComponent() {
  return (
    <PerformanceWrapper id="my-component">
      <ExpensiveComponent />
    </PerformanceWrapper>
  )
}
```

## Logger Usage

```typescript
import { logger } from '@/lib/debug/logger'

// Different log levels
logger.debug('Debug message', data)
logger.info('Info message', data)
logger.warn('Warning message', data)
logger.error('Error message', data)
logger.performance('Operation completed', startTime)
```

## Troubleshooting

### Debug Panel Not Showing
- Ensure you're in development mode (`NODE_ENV=development`)
- Check browser console for errors
- Verify debug components are imported in layout

### Performance Warnings
- Check for unnecessary re-renders
- Use React DevTools Profiler
- Wrap expensive components with PerformanceWrapper

### Network Issues
- Check browser Network tab
- Verify API endpoints are correct
- Look for CORS issues in console

## Best Practices

1. **Use debug hooks sparingly** - Only for components you're actively debugging
2. **Remove debug code before production** - Debug tools are automatically disabled in production
3. **Check performance regularly** - Use the debug panel to monitor render times
4. **Export debug data** - Use the export button in debug panel for bug reports

## Scripts

- `npm run dev:debug` - Start with debugging enabled
- `npm run test:debug` - Run tests with verbose output
- `npm run debug:bundle` - Analyze bundle size
- `npm run debug:performance` - Test production performance
EOF

print_status "Created DEBUG_README.md"

# Run the test
print_info "Running debug integration test..."
if node debug-test.js; then
    print_status "Debug integration test passed!"
else
    print_error "Debug integration test failed. Check the setup."
fi

# Clean up test file
rm debug-test.js

# Final instructions
echo ""
echo "🎉 Debug Integration Setup Complete!"
echo ""
print_info "Next steps:"
echo "  1. Start development: npm run dev:debug"
echo "  2. Open your browser and look for the debug panel"
echo "  3. Check the console for debug messages"
echo "  4. Read DEBUG_README.md for detailed usage"
echo ""
print_status "Happy debugging! 🐛"
EOF

"use client"

import React, { memo, lazy, Suspense, Component, type ComponentType, type ReactNode } from 'react'

// Lazy load recharts to reduce initial bundle size
const RechartsComponents = lazy(() =>
  import('recharts').then(module => {
    // Create a wrapper component that provides all recharts components
    const ChartComponents = (props: { children: (components: any) => ReactNode }) => {
      return props.children({
        // Chart components
        BarChart: module.BarChart,
        LineChart: module.LineChart,
        PieChart: module.PieChart,
        ScatterChart: module.ScatterChart,
        AreaChart: module.AreaChart,
        ComposedChart: module.ComposedChart,
        FunnelChart: module.FunnelChart,

        // Container and layout
        ResponsiveContainer: module.ResponsiveContainer,

        // Axes and grids
        CartesianGrid: module.CartesianGrid,
        XAxis: module.XAxis,
        YAxis: module.YAxis,
        ZAxis: module.ZAxis,

        // Interactive components
        Tooltip: module.Tooltip,
        Legend: module.Legend,
        Brush: module.Brush,

        // Chart elements
        Bar: module.Bar,
        Line: module.Line,
        Area: module.Area,
        Pie: module.Pie,
        Cell: module.Cell,
        Scatter: module.Scatter,
        Funnel: module.Funnel,

        // Reference components
        ReferenceLine: module.ReferenceLine,
        ReferenceArea: module.ReferenceArea,
        ReferenceDot: module.ReferenceDot,

        // Error bar
        ErrorBar: module.ErrorBar
      })
    }

    return { default: ChartComponents }
  })
)

// Chart loading skeleton component
export const ChartSkeleton = memo(function ChartSkeleton({ 
  height = 350,
  className = ""
}: { 
  height?: number
  className?: string 
}) {
  return (
    <div 
      className={`w-full bg-muted animate-pulse rounded flex items-center justify-center ${className}`}
      style={{ height }}
    >
      <div className="text-muted-foreground text-sm">Loading chart...</div>
    </div>
  )
})

// Error boundary for chart components
interface ChartErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ChartErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: any) => void
}

class ChartErrorBoundary extends Component<ChartErrorBoundaryProps, ChartErrorBoundaryState> {
  constructor(props: ChartErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ChartErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Chart Error:', error, errorInfo)
    this.props.onError?.(error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="h-[350px] w-full bg-muted rounded flex flex-col items-center justify-center border border-destructive/20">
          <div className="text-destructive text-sm font-medium">Chart failed to load</div>
          <div className="text-muted-foreground text-xs mt-1">
            {this.state.error?.message || 'Unknown error occurred'}
          </div>
          <button 
            className="text-xs text-primary hover:underline mt-2"
            onClick={() => this.setState({ hasError: false, error: undefined })}
          >
            Try again
          </button>
        </div>
      )
    }

    return this.props.children
  }
}

// Chart provider wrapper component
interface ChartProviderProps {
  children: ReactNode
  fallback?: ReactNode
  height?: number
  onError?: (error: Error, errorInfo: any) => void
}

export function ChartProvider({ 
  children, 
  fallback, 
  height = 350,
  onError 
}: ChartProviderProps) {
  return (
    <ChartErrorBoundary 
      fallback={fallback || <ChartSkeleton height={height} />}
      onError={onError}
    >
      <Suspense fallback={<ChartSkeleton height={height} />}>
        {children}
      </Suspense>
    </ChartErrorBoundary>
  )
}

// Higher-order component for chart wrapping
export function withChartProvider<P extends object>(
  WrappedComponent: ComponentType<P>,
  options?: {
    height?: number
    fallback?: ReactNode
    onError?: (error: Error, errorInfo: any) => void
  }
) {
  const ChartWrappedComponent = (props: P) => (
    <ChartProvider 
      height={options?.height}
      fallback={options?.fallback}
      onError={options?.onError}
    >
      <WrappedComponent {...props} />
    </ChartProvider>
  )

  ChartWrappedComponent.displayName = `withChartProvider(${WrappedComponent.displayName || WrappedComponent.name})`
  
  return ChartWrappedComponent
}

// Optimized chart component factory
interface OptimizedChartProps {
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'area' | 'composed' | 'funnel'
  data: any[]
  config?: any
  height?: number
  width?: string | number
  className?: string
  [key: string]: any
}

export const OptimizedChart = memo(function OptimizedChart({
  type,
  data,
  config = {},
  height = 350,
  width = "100%",
  className = "",
  ...props
}: OptimizedChartProps) {
  return (
    <ChartProvider height={height}>
      <RechartsComponents>
        {(components: any) => {
          const ChartComponent = {
            bar: components.BarChart,
            line: components.LineChart,
            pie: components.PieChart,
            scatter: components.ScatterChart,
            area: components.AreaChart,
            composed: components.ComposedChart,
            funnel: components.FunnelChart
          }[type]

          if (!ChartComponent) {
            throw new Error(`Chart type "${type}" is not supported`)
          }

          return (
            <div className={className} style={{ height, width }}>
              <components.ResponsiveContainer width="100%" height="100%">
                <ChartComponent data={data} {...config} {...props}>
                  {config.children}
                </ChartComponent>
              </components.ResponsiveContainer>
            </div>
          )
        }}
      </RechartsComponents>
    </ChartProvider>
  )
})

// Export recharts components for direct use
export { RechartsComponents }

// Utility function to create memoized chart components
export function createMemoizedChart<T extends object>(
  chartRenderer: (props: T, components: any) => ReactNode,
  displayName?: string
) {
  const MemoizedChart = memo(function MemoizedChartComponent(props: T) {
    return (
      <ChartProvider>
        <RechartsComponents>
          {(components: any) => chartRenderer(props, components)}
        </RechartsComponents>
      </ChartProvider>
    )
  })

  if (displayName) {
    MemoizedChart.displayName = displayName
  }

  return MemoizedChart
}

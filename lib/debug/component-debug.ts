import { useEffect, useRef } from 'react';
import { logger } from './logger';

// Store for component debug info
const componentDebugStore: Record<string, any> = {};

export function useComponentDebug(componentName: string, props: any = {}): void {
  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());

  // Only enable in development with debug enabled
  const isEnabled = process.env.NODE_ENV === 'development' && process.env.ENABLE_PERFORMANCE_METRICS === 'true';

  // Simplified tracking with reduced overhead
  useEffect(() => {
    if (!isEnabled) return;

    renderCount.current += 1;

    // Only store essential component info
    componentDebugStore[componentName] = {
      name: componentName,
      renderCount: renderCount.current,
      mountTime: mountTime.current,
      lastRenderTime: Date.now(),
    };

    // Cleanup on unmount
    return () => {
      // Mark as unmounted but keep in store for debugging
      if (componentDebugStore[componentName]) {
        componentDebugStore[componentName].unmounted = true;
        componentDebugStore[componentName].unmountTime = Date.now();
      }
    };
  }, [componentName, isEnabled]); // Removed props dependency to reduce re-renders
}

export function getComponentDebugInfo(): {
  components: Array<{
    name: string;
    instances: number;
    renders: number;
    mountTime: number;
    lastRenderTime: number;
    unmounted?: boolean;
    unmountTime?: number;
  }>;
  totalComponents: number;
  totalInstances: number;
  totalRenders: number;
} {
  const components = Object.values(componentDebugStore).map(comp => ({
    name: comp.name,
    instances: 1, // Each entry represents one instance
    renders: comp.renderCount,
    mountTime: comp.mountTime,
    lastRenderTime: comp.lastRenderTime,
    unmounted: comp.unmounted,
    unmountTime: comp.unmountTime
  }));

  const totalComponents = Object.keys(componentDebugStore).length;
  const totalInstances = components.length; // Same as totalComponents since each entry is one instance
  const totalRenders = components.reduce((sum, comp) => sum + comp.renders, 0);

  return {
    components,
    totalComponents,
    totalInstances,
    totalRenders
  };
}

export function clearComponentDebugInfo(): void {
  Object.keys(componentDebugStore).forEach(key => {
    delete componentDebugStore[key];
  });
}
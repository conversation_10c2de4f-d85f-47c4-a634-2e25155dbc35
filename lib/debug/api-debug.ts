import { logger } from './logger';

// Store for API debug info
const apiDebugStore: Record<string, any> = {};
const apiPerformanceMetrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  totalTime: 0,
  averageTime: 0,
  slowestRequest: { url: '', time: 0 },
  fastestRequest: { url: '', time: Infinity }
};

// Initialize API debugging
export default function initApiDebug() {
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    return;
  }

  // Don't initialize API debugging to improve performance
  console.warn('API debugging disabled for better performance');

  // Initialize empty metrics
  apiPerformanceMetrics.totalRequests = 0;
  apiPerformanceMetrics.successfulRequests = 0;
  apiPerformanceMetrics.failedRequests = 0;
  apiPerformanceMetrics.totalTime = 0;
  apiPerformanceMetrics.averageTime = 0;
  apiPerformanceMetrics.slowestRequest = { url: '', time: 0 };
  apiPerformanceMetrics.fastestRequest = { url: '', time: Infinity };
}

function updateApiMetrics(url: string, duration: number, success: boolean) {
  apiPerformanceMetrics.totalRequests++;
  apiPerformanceMetrics.totalTime += duration;
  
  if (success) {
    apiPerformanceMetrics.successfulRequests++;
  } else {
    apiPerformanceMetrics.failedRequests++;
  }
  
  apiPerformanceMetrics.averageTime = 
    apiPerformanceMetrics.totalTime / apiPerformanceMetrics.totalRequests;
  
  if (duration > apiPerformanceMetrics.slowestRequest.time) {
    apiPerformanceMetrics.slowestRequest = { url, time: duration };
  }
  
  if (duration < apiPerformanceMetrics.fastestRequest.time) {
    apiPerformanceMetrics.fastestRequest = { url, time: duration };
  }
}

export function getApiDebugInfo(): Record<string, any> {
  return { ...apiDebugStore };
}

export function clearApiDebugInfo(): void {
  Object.keys(apiDebugStore).forEach(key => {
    delete apiDebugStore[key];
  });
}

export function getApiPerformanceMetrics() {
  return { ...apiPerformanceMetrics };
}
#!/usr/bin/env node

/**
 * Test script to verify dashboard routes are working without streaming errors
 */

const http = require('http');

const routes = [
  '/dashboard',
  '/dashboard/analytics',
  '/dashboard/content',
  '/dashboard/performance',
  '/dashboard/audience',
  '/dashboard/campaigns',
  '/dashboard/calendar',
  '/dashboard/engagement',
  '/dashboard/notifications',
  '/dashboard/settings'
];

const baseUrl = 'http://localhost:3001';

async function testRoute(route) {
  return new Promise((resolve) => {
    const startTime = Date.now();
    
    const req = http.get(`${baseUrl}${route}`, (res) => {
      const duration = Date.now() - startTime;
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          route,
          status: res.statusCode,
          duration,
          success: res.statusCode === 200,
          hasStreamingError: data.includes('Stream is already ended') || 
                           data.includes('ERR_STREAM_ALREADY_FINISHED') ||
                           data.includes('failed to pipe response')
        });
      });
    });
    
    req.on('error', (err) => {
      resolve({
        route,
        status: 'ERROR',
        duration: Date.now() - startTime,
        success: false,
        error: err.message,
        hasStreamingError: err.message.includes('Stream') || err.message.includes('pipe')
      });
    });
    
    req.setTimeout(30000, () => {
      req.destroy();
      resolve({
        route,
        status: 'TIMEOUT',
        duration: 30000,
        success: false,
        hasStreamingError: false
      });
    });
  });
}

async function runTests() {
  console.log('🧪 Testing dashboard routes for streaming errors...\n');
  
  const results = [];
  
  for (const route of routes) {
    process.stdout.write(`Testing ${route}... `);
    const result = await testRoute(route);
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${result.status} (${result.duration}ms)`);
    } else {
      console.log(`❌ ${result.status} (${result.duration}ms)${result.error ? ` - ${result.error}` : ''}`);
    }
    
    if (result.hasStreamingError) {
      console.log(`   ⚠️  Streaming error detected!`);
    }
  }
  
  console.log('\n📊 Test Summary:');
  console.log(`Total routes tested: ${results.length}`);
  console.log(`Successful: ${results.filter(r => r.success).length}`);
  console.log(`Failed: ${results.filter(r => !r.success).length}`);
  console.log(`Streaming errors: ${results.filter(r => r.hasStreamingError).length}`);
  
  const avgDuration = results.filter(r => r.success).reduce((sum, r) => sum + r.duration, 0) / results.filter(r => r.success).length;
  console.log(`Average response time: ${avgDuration.toFixed(0)}ms`);
  
  if (results.some(r => r.hasStreamingError)) {
    console.log('\n❌ Streaming errors detected! Check the routes above.');
    process.exit(1);
  } else {
    console.log('\n✅ All tests passed! No streaming errors detected.');
  }
}

runTests().catch(console.error);

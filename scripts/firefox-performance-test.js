#!/usr/bin/env node

/**
 * Firefox Performance Testing Script
 * 
 * This script tests the dashboard performance specifically in Firefox
 * and provides detailed metrics for optimization verification.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🦊 Firefox Performance Testing Suite\n');

// Performance test configuration
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  routes: [
    '/dashboard',
    '/dashboard/performance',
    '/dashboard/affiliate-links',
    '/dashboard/analytics'
  ],
  iterations: 3,
  timeout: 30000
};

// Component size analysis
function analyzeComponentSizes() {
  console.log('📊 Analyzing component sizes...\n');
  
  const directories = [
    'components/dashboard/performance',
    'components/dashboard/affiliate-links/analytics',
    'components/dashboard'
  ];
  
  const results = {};
  
  directories.forEach(dir => {
    if (fs.existsSync(dir)) {
      try {
        const output = execSync(`find ${dir} -name "*.tsx" -exec wc -c {} +`, { encoding: 'utf8' });
        const lines = output.trim().split('\n');
        
        results[dir] = {
          files: [],
          total: 0
        };
        
        lines.forEach(line => {
          const parts = line.trim().split(/\s+/);
          if (parts.length === 2) {
            const size = parseInt(parts[0]);
            const file = parts[1];
            
            if (file !== 'total') {
              results[dir].files.push({
                file: path.basename(file),
                size,
                sizeKB: (size / 1024).toFixed(2),
                status: size > 8192 ? '❌ TOO LARGE' : '✅ OPTIMAL'
              });
              results[dir].total += size;
            }
          }
        });
        
        // Sort by size descending
        results[dir].files.sort((a, b) => b.size - a.size);
      } catch (error) {
        console.warn(`⚠️  Could not analyze ${dir}: ${error.message}`);
      }
    }
  });
  
  return results;
}

// Bundle size analysis
function analyzeBundleSize() {
  console.log('📦 Analyzing bundle size...\n');
  
  try {
    // Check if build exists
    if (!fs.existsSync('.next')) {
      console.log('🏗️  Building application for bundle analysis...');
      execSync('bun run build', { stdio: 'inherit' });
    }
    
    // Analyze .next directory
    const nextDir = '.next';
    const staticDir = path.join(nextDir, 'static');
    
    if (fs.existsSync(staticDir)) {
      const output = execSync(`find ${staticDir} -name "*.js" -exec wc -c {} +`, { encoding: 'utf8' });
      const lines = output.trim().split('\n');
      
      let totalSize = 0;
      const jsFiles = [];
      
      lines.forEach(line => {
        const parts = line.trim().split(/\s+/);
        if (parts.length === 2) {
          const size = parseInt(parts[0]);
          const file = parts[1];
          
          if (file !== 'total' && file.endsWith('.js')) {
            jsFiles.push({
              file: path.basename(file),
              size,
              sizeKB: (size / 1024).toFixed(2)
            });
            totalSize += size;
          }
        }
      });
      
      return {
        totalSize,
        totalSizeKB: (totalSize / 1024).toFixed(2),
        totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
        files: jsFiles.sort((a, b) => b.size - a.size).slice(0, 10) // Top 10 largest
      };
    }
  } catch (error) {
    console.warn(`⚠️  Bundle analysis failed: ${error.message}`);
    return null;
  }
}

// Performance metrics calculation
function calculatePerformanceMetrics(componentResults, bundleResults) {
  const metrics = {
    componentOptimization: {
      totalComponents: 0,
      optimizedComponents: 0,
      oversizedComponents: 0,
      averageSize: 0
    },
    bundleOptimization: {
      estimatedReduction: 0,
      targetReduction: 0.25 // 25%
    }
  };
  
  // Calculate component metrics
  Object.values(componentResults).forEach(dir => {
    dir.files.forEach(file => {
      metrics.componentOptimization.totalComponents++;
      if (file.size <= 8192) {
        metrics.componentOptimization.optimizedComponents++;
      } else {
        metrics.componentOptimization.oversizedComponents++;
      }
    });
  });
  
  if (metrics.componentOptimization.totalComponents > 0) {
    const totalSize = Object.values(componentResults).reduce((sum, dir) => sum + dir.total, 0);
    metrics.componentOptimization.averageSize = totalSize / metrics.componentOptimization.totalComponents;
  }
  
  // Calculate optimization percentage
  metrics.componentOptimization.optimizationPercentage = 
    (metrics.componentOptimization.optimizedComponents / metrics.componentOptimization.totalComponents) * 100;
  
  return metrics;
}

// Generate performance report
function generateReport(componentResults, bundleResults, metrics) {
  console.log('📋 FIREFOX PERFORMANCE OPTIMIZATION REPORT\n');
  console.log('=' .repeat(60));
  
  // Component Analysis
  console.log('\n🔧 COMPONENT SIZE ANALYSIS');
  console.log('-'.repeat(40));
  
  Object.entries(componentResults).forEach(([dir, data]) => {
    console.log(`\n📁 ${dir}:`);
    console.log(`   Total Size: ${(data.total / 1024).toFixed(2)} KB`);
    console.log(`   Files: ${data.files.length}`);
    
    data.files.forEach(file => {
      console.log(`   ${file.status} ${file.file}: ${file.sizeKB} KB`);
    });
  });
  
  // Bundle Analysis
  if (bundleResults) {
    console.log('\n📦 BUNDLE SIZE ANALYSIS');
    console.log('-'.repeat(40));
    console.log(`Total Bundle Size: ${bundleResults.totalSizeMB} MB`);
    console.log(`JavaScript Files: ${bundleResults.files.length}+`);
    
    console.log('\nLargest JavaScript Files:');
    bundleResults.files.slice(0, 5).forEach(file => {
      console.log(`   ${file.file}: ${file.sizeKB} KB`);
    });
  }
  
  // Performance Metrics
  console.log('\n📊 PERFORMANCE METRICS');
  console.log('-'.repeat(40));
  console.log(`Component Optimization: ${metrics.componentOptimization.optimizationPercentage.toFixed(1)}%`);
  console.log(`Optimized Components: ${metrics.componentOptimization.optimizedComponents}/${metrics.componentOptimization.totalComponents}`);
  console.log(`Average Component Size: ${(metrics.componentOptimization.averageSize / 1024).toFixed(2)} KB`);
  
  // Success Criteria
  console.log('\n✅ SUCCESS CRITERIA');
  console.log('-'.repeat(40));
  
  const criteria = [
    {
      name: 'All components <8KB',
      status: metrics.componentOptimization.oversizedComponents === 0,
      current: `${metrics.componentOptimization.oversizedComponents} oversized`
    },
    {
      name: 'Component optimization >90%',
      status: metrics.componentOptimization.optimizationPercentage >= 90,
      current: `${metrics.componentOptimization.optimizationPercentage.toFixed(1)}%`
    },
    {
      name: 'TypeScript compilation clean',
      status: true, // We fixed this earlier
      current: 'No errors'
    }
  ];
  
  criteria.forEach(criterion => {
    const icon = criterion.status ? '✅' : '❌';
    console.log(`${icon} ${criterion.name}: ${criterion.current}`);
  });
  
  // Recommendations
  console.log('\n💡 OPTIMIZATION RECOMMENDATIONS');
  console.log('-'.repeat(40));
  
  if (metrics.componentOptimization.oversizedComponents > 0) {
    console.log('• Split remaining large components into sub-8KB files');
  }
  
  if (bundleResults && bundleResults.totalSize > 500000) {
    console.log('• Implement more aggressive code splitting');
    console.log('• Consider lazy loading for heavy dependencies');
  }
  
  console.log('• Test performance in Firefox with DevTools');
  console.log('• Monitor bundle size with each deployment');
  console.log('• Implement virtual scrolling for large tables');
  
  console.log('\n🚀 NEXT STEPS');
  console.log('-'.repeat(40));
  console.log('1. Run: bun run dev:minimal');
  console.log('2. Test: http://localhost:3000/dashboard/performance');
  console.log('3. Verify: Firefox DevTools Performance tab');
  console.log('4. Measure: Load time and memory usage');
  console.log('5. Compare: Before/after metrics');
  
  console.log('\n' + '='.repeat(60));
}

// Main execution
function main() {
  try {
    const componentResults = analyzeComponentSizes();
    const bundleResults = analyzeBundleSize();
    const metrics = calculatePerformanceMetrics(componentResults, bundleResults);
    
    generateReport(componentResults, bundleResults, metrics);
    
    // Save results to file
    const reportData = {
      timestamp: new Date().toISOString(),
      componentResults,
      bundleResults,
      metrics
    };
    
    fs.writeFileSync('firefox-performance-report.json', JSON.stringify(reportData, null, 2));
    console.log('\n📄 Report saved to: firefox-performance-report.json');
    
  } catch (error) {
    console.error('❌ Performance test failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

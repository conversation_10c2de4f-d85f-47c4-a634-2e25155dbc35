#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Quick Bundle Optimization Script\n');

// Step 1: Clean cache
console.log('1️⃣ Cleaning development cache...');
try {
  execSync('node scripts/cache-manager.js clean', { stdio: 'inherit' });
  console.log('✅ Cache cleaned successfully\n');
} catch (error) {
  console.log('❌ Cache cleaning failed:', error.message);
}

// Step 2: Analyze current state
console.log('2️⃣ Analyzing current bundle state...');
try {
  execSync('node scripts/analyze-dependencies.js', { stdio: 'inherit' });
  console.log('✅ Analysis complete\n');
} catch (error) {
  console.log('❌ Analysis failed:', error.message);
}

// Step 3: Update Next.js config for better tree-shaking
console.log('3️⃣ Updating Next.js configuration...');

const nextConfigPath = 'next.config.mjs';
const nextConfig = fs.readFileSync(nextConfigPath, 'utf8');

// Check if modularizeImports is already configured
if (!nextConfig.includes('modularizeImports')) {
  console.log('Adding modularizeImports configuration...');
  
  const modularizeImportsConfig = `
    // Enable modern JavaScript for supported browsers
    modularizeImports: {
      'lucide-react': {
        transform: 'lucide-react/dist/esm/icons/{{kebabCase member}}',
      },
      'date-fns': {
        transform: 'date-fns/{{member}}',
      },
    },`;
  
  // Insert after optimizePackageImports
  const updatedConfig = nextConfig.replace(
    /optimizePackageImports: \[[\s\S]*?\],/,
    `$&${modularizeImportsConfig}`
  );
  
  if (updatedConfig !== nextConfig) {
    fs.writeFileSync(nextConfigPath, updatedConfig);
    console.log('✅ Next.js config updated for better tree-shaking');
  } else {
    console.log('⚠️ Could not automatically update config - manual update needed');
  }
} else {
  console.log('✅ modularizeImports already configured');
}

// Step 4: Create optimized icon bundle
console.log('\n4️⃣ Creating optimized icon bundle...');

try {
  // Find all lucide-react imports
  const result = execSync('grep -r "from.*lucide-react" app/ components/ lib/ hooks/ --include="*.tsx" --include="*.ts" 2>/dev/null || true', { encoding: 'utf8' });
  const lines = result.split('\n').filter(line => line.trim());
  
  const allIcons = new Set();
  lines.forEach(line => {
    const match = line.match(/import\s*{([^}]+)}/);
    if (match) {
      const icons = match[1].split(',').map(icon => icon.trim());
      icons.forEach(icon => allIcons.add(icon));
    }
  });
  
  if (allIcons.size > 0) {
    const iconBundleContent = `// Auto-generated optimized icon bundle
// This file exports only the icons used in the application
// Generated on: ${new Date().toISOString()}

${Array.from(allIcons).sort().map(icon => {
  const kebabCase = icon.replace(/([A-Z])/g, '-$1').toLowerCase().substring(1);
  return `export { ${icon} } from 'lucide-react/dist/esm/icons/${kebabCase}';`;
}).join('\n')}

// Usage: import { ChevronDown, User } from '@/lib/icons';
`;
    
    // Create lib directory if it doesn't exist
    if (!fs.existsSync('lib')) {
      fs.mkdirSync('lib', { recursive: true });
    }
    
    fs.writeFileSync('lib/icons.ts', iconBundleContent);
    console.log(`✅ Created optimized icon bundle with ${allIcons.size} icons`);
    console.log('   📁 File: lib/icons.ts');
    console.log('   💡 Update imports to use: import { IconName } from "@/lib/icons"');
  } else {
    console.log('⚠️ No lucide-react imports found');
  }
} catch (error) {
  console.log('❌ Icon bundle creation failed:', error.message);
}

// Step 5: Add bundle size monitoring script
console.log('\n5️⃣ Setting up bundle size monitoring...');

const packageJsonPath = 'package.json';
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Add monitoring scripts if they don't exist
const newScripts = {
  'bundle:analyze': 'ANALYZE=true bun run build',
  'bundle:size': 'find .next/static/chunks -name "*.js" -exec du -b {} \\; | awk \'{sum += $1} END {print "Total JS: " sum/1024/1024 " MB"}\'',
  'bundle:monitor': 'node scripts/analyze-dependencies.js && npm run bundle:size',
  'optimize:quick': 'node scripts/quick-optimize.js'
};

let scriptsUpdated = false;
Object.entries(newScripts).forEach(([script, command]) => {
  if (!packageJson.scripts[script]) {
    packageJson.scripts[script] = command;
    scriptsUpdated = true;
  }
});

if (scriptsUpdated) {
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
  console.log('✅ Added bundle monitoring scripts to package.json');
} else {
  console.log('✅ Bundle monitoring scripts already exist');
}

// Step 6: Final analysis
console.log('\n6️⃣ Running final analysis...');
try {
  console.log('\n📊 Current bundle size:');
  execSync('npm run bundle:size', { stdio: 'inherit' });
} catch (error) {
  console.log('⚠️ Could not analyze bundle size - run "npm run build" first');
}

// Summary
console.log('\n🎉 Quick Optimization Complete!');
console.log('=' .repeat(50));
console.log('✅ Development cache cleaned');
console.log('✅ Next.js config optimized for tree-shaking');
console.log('✅ Optimized icon bundle created');
console.log('✅ Bundle monitoring scripts added');
console.log('\n📋 Next Steps:');
console.log('1. Update icon imports to use lib/icons.ts');
console.log('2. Run "npm run build" to test optimizations');
console.log('3. Use "npm run bundle:monitor" for regular monitoring');
console.log('4. Consider replacing date-fns with dayjs for further savings');
console.log('\n📖 See BUNDLE_SIZE_OPTIMIZATION_STRATEGY.mdx for detailed guide');

#!/usr/bin/env node

/**
 * Development Environment Optimization Script
 * 
 * This script helps optimize the Next.js development environment
 * for faster compilation and better performance.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Optimizing development environment...\n');

// Check if next-dev-performance is installed
try {
  require.resolve('next-dev-performance');
} catch (error) {
  console.log('📦 Installing next-dev-performance...');
  execSync('bun add -D next-dev-performance', { stdio: 'inherit' });
}

// Create a temporary next.config.js with dev performance optimizations
const originalConfig = fs.readFileSync('next.config.mjs', 'utf8');
const optimizedConfig = `
import withDevPerformance from 'next-dev-performance';

${originalConfig.replace('export default nextConfig', `
// Apply development performance optimizations
const config = process.env.NODE_ENV === 'development' 
  ? withDevPerformance(nextConfig, {
      // Disable type checking during development for faster compilation
      disableTypeChecking: true,
      
      // Disable ESLint during development for faster compilation
      disableEslint: true,
      
      // Disable React strict mode during development for fewer re-renders
      disableReactStrictMode: true,
      
      // Disable source maps during development for faster compilation
      disableSourceMaps: true,
    })
  : nextConfig;

export default config;
`)}
`;

// Write temporary config
const tempConfigPath = 'next.config.optimized.mjs';
fs.writeFileSync(tempConfigPath, optimizedConfig);

console.log('✅ Created optimized development configuration');
console.log(`📄 Saved to: ${tempConfigPath}`);

// Create a script to run the optimized development server
const devScript = `#!/bin/bash
export NEXT_CONFIG_FILE=next.config.optimized.mjs
export NODE_ENV=development
bun --bun run next dev
`;

const devScriptPath = 'dev-optimized.sh';
fs.writeFileSync(devScriptPath, devScript);
fs.chmodSync(devScriptPath, '755');

console.log('✅ Created optimized development script');
console.log(`📄 Saved to: ${devScriptPath}`);

// Update package.json to add the optimized dev script
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  if (!packageJson.scripts) {
    packageJson.scripts = {};
  }
  
  packageJson.scripts['dev:optimized'] = 'bash ./dev-optimized.sh';
  
  fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
  console.log('✅ Added "dev:optimized" script to package.json');
} catch (error) {
  console.error('❌ Failed to update package.json:', error.message);
}

console.log('\n🎉 Optimization complete!');
console.log('Run "bun run dev:optimized" to start the optimized development server.');

// Additional performance tips
console.log('\n💡 Development Performance Tips:');
console.log('1. Use the optimized development server for faster compilation');
console.log('2. Disable unnecessary browser extensions during development');
console.log('3. Increase Node.js memory limit with NODE_OPTIONS="--max-old-space-size=8192"');
console.log('4. Consider using a RAM disk for the .next directory');
console.log('5. Use incremental builds when possible');
console.log('6. Keep your dependencies up to date');

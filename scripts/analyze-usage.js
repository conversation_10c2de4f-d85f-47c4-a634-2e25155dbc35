#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Analyzing Dependency Usage\n');

// Heavy dependencies to analyze
const heavyDeps = [
  { name: 'lucide-react', size: '23.51 MB' },
  { name: 'date-fns', size: '21.13 MB' },
  { name: 'recharts', size: '4.43 MB' },
  { name: '@radix-ui', size: '2.30 MB' }
];

// Function to find imports in files
function findImports(pattern, description) {
  try {
    const result = execSync(`grep -r "${pattern}" app/ components/ lib/ hooks/ --include="*.tsx" --include="*.ts" 2>/dev/null || true`, { encoding: 'utf8' });
    const lines = result.split('\n').filter(line => line.trim() && !line.includes('node_modules'));
    
    console.log(`\n📦 ${description}:`);
    console.log('=' .repeat(60));
    
    if (lines.length === 0) {
      console.log('  ❌ No usage found');
      return [];
    }
    
    // Extract unique imports
    const imports = new Set();
    lines.forEach(line => {
      const match = line.match(/import.*from.*['"`]([^'"`]+)['"`]/);
      if (match) {
        imports.add(match[1]);
      }
      
      // Also check for destructured imports
      const destructuredMatch = line.match(/import\s*{([^}]+)}/);
      if (destructuredMatch) {
        const items = destructuredMatch[1].split(',').map(item => item.trim());
        items.forEach(item => imports.add(item));
      }
    });
    
    console.log(`  Found ${lines.length} import statements in ${new Set(lines.map(l => l.split(':')[0])).size} files`);
    console.log(`  Unique imports: ${imports.size}`);
    
    // Show first few files for reference
    const files = [...new Set(lines.map(l => l.split(':')[0]))].slice(0, 5);
    files.forEach(file => console.log(`    - ${file}`));
    
    if (files.length < lines.length) {
      console.log(`    ... and ${new Set(lines.map(l => l.split(':')[0])).size - files.length} more files`);
    }
    
    return [...imports];
  } catch (error) {
    console.log(`  ❌ Error analyzing: ${error.message}`);
    return [];
  }
}

// Analyze lucide-react usage
const lucideImports = findImports('from.*lucide-react', 'Lucide React Icons (23.51 MB)');

// Analyze date-fns usage
const dateFnsImports = findImports('from.*date-fns', 'Date-fns (21.13 MB)');

// Analyze recharts usage
const rechartsImports = findImports('from.*recharts', 'Recharts (4.43 MB)');

// Analyze @radix-ui usage
const radixImports = findImports('from.*@radix-ui', 'Radix UI Components (2.30 MB total)');

// Optimization recommendations
console.log('\n🎯 Optimization Recommendations:');
console.log('=' .repeat(60));

// Lucide React optimization
if (lucideImports.length > 0) {
  console.log('\n📦 Lucide React (23.51 MB → Potential savings: ~20 MB)');
  console.log('  Current: Importing entire lucide-react package');
  console.log('  Recommendation: Use individual icon imports');
  console.log('  Example: import { ChevronDown } from "lucide-react/dist/esm/icons/chevron-down"');
  console.log('  Or: Create a custom icon bundle with only needed icons');
}

// Date-fns optimization
if (dateFnsImports.length > 0) {
  console.log('\n📅 Date-fns (21.13 MB → Potential savings: ~18 MB)');
  console.log('  Current: Importing from date-fns');
  console.log('  Recommendation: Use individual function imports');
  console.log('  Example: import { format } from "date-fns/format"');
  console.log('  Or: Consider lighter alternatives like dayjs (~2KB)');
}

// Recharts optimization
if (rechartsImports.length > 0) {
  console.log('\n📊 Recharts (4.43 MB → Already optimized with ChartProvider)');
  console.log('  Current: Using ChartProvider for lazy loading');
  console.log('  Status: ✅ Already optimized');
}

// Check for unused @radix-ui components
console.log('\n🎨 Radix UI Components Analysis:');
const radixComponents = [
  'accordion', 'alert-dialog', 'aspect-ratio', 'avatar', 'checkbox',
  'collapsible', 'context-menu', 'dialog', 'dropdown-menu', 'hover-card',
  'label', 'menubar', 'navigation-menu', 'popover', 'progress',
  'radio-group', 'scroll-area', 'select', 'separator', 'slider',
  'slot', 'switch', 'tabs', 'toast', 'toggle', 'toggle-group', 'tooltip'
];

const usedRadixComponents = [];
const unusedRadixComponents = [];

radixComponents.forEach(component => {
  try {
    const result = execSync(`grep -r "@radix-ui/react-${component}" app/ components/ lib/ hooks/ --include="*.tsx" --include="*.ts" 2>/dev/null || true`, { encoding: 'utf8' });
    if (result.trim()) {
      usedRadixComponents.push(component);
    } else {
      unusedRadixComponents.push(component);
    }
  } catch (error) {
    unusedRadixComponents.push(component);
  }
});

console.log(`  ✅ Used components (${usedRadixComponents.length}): ${usedRadixComponents.join(', ')}`);
console.log(`  ❌ Unused components (${unusedRadixComponents.length}): ${unusedRadixComponents.join(', ')}`);

if (unusedRadixComponents.length > 0) {
  const estimatedSavings = unusedRadixComponents.length * 0.1; // Rough estimate
  console.log(`  💾 Potential savings: ~${estimatedSavings.toFixed(1)} MB by removing unused components`);
}

// Bundle size summary
console.log('\n📊 Bundle Size Breakdown:');
console.log('=' .repeat(60));
console.log('Current production bundle: ~2 MB (JavaScript)');
console.log('Cache directory: 349 MB (development artifacts)');
console.log('Total .next directory: 387 MB');
console.log('\nThe 19.77 MB mentioned likely refers to:');
console.log('  - Development build artifacts');
console.log('  - Uncompressed bundle size');
console.log('  - Or total static assets including images/fonts');

console.log('\n🎯 Priority Optimizations:');
console.log('1. Implement tree-shaking for lucide-react (save ~20 MB)');
console.log('2. Use individual date-fns imports (save ~18 MB)');
console.log('3. Remove unused @radix-ui components (save ~1-2 MB)');
console.log('4. Clean webpack cache regularly');
console.log('5. Consider using lighter alternatives for heavy dependencies');

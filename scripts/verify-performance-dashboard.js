#!/usr/bin/env node

/**
 * Performance Dashboard Verification Script
 * 
 * This script verifies that the performance dashboard is working correctly
 * and measures key performance metrics.
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function verifyPerformanceDashboard() {
  console.log('🚀 Starting Performance Dashboard Verification...\n');
  
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  // Enable performance monitoring
  await page.setCacheEnabled(false);
  
  const metrics = {
    loadTime: 0,
    errors: [],
    components: [],
    chartElements: 0,
    memoryUsage: 0,
    success: false
  };

  try {
    console.log('📊 Testing Performance Dashboard...');
    
    // Navigate to performance dashboard
    const startTime = Date.now();
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        metrics.errors.push(msg.text());
      }
    });
    
    page.on('pageerror', (error) => {
      metrics.errors.push(error.message);
    });
    
    const response = await page.goto('http://localhost:3000/dashboard/performance', {
      waitUntil: 'networkidle0',
      timeout: 30000
    });
    
    const endTime = Date.now();
    metrics.loadTime = endTime - startTime;
    
    console.log(`✅ Page loaded successfully in ${metrics.loadTime}ms`);
    console.log(`📈 Response status: ${response.status()}`);
    
    // Wait for components to load
    await page.waitForTimeout(2000);
    
    // Check for key dashboard elements
    const dashboardElements = await page.evaluate(() => {
      const elements = {
        header: !!document.querySelector('[data-testid="performance-header"], h1, .performance-header'),
        charts: document.querySelectorAll('svg, canvas, .recharts-wrapper, [data-testid*="chart"]').length,
        sections: document.querySelectorAll('section, .dashboard-section, [class*="section"]').length,
        buttons: document.querySelectorAll('button').length,
        loading: document.querySelectorAll('[data-testid*="skeleton"], .loading, .spinner').length
      };
      
      return elements;
    });
    
    metrics.components = Object.keys(dashboardElements);
    metrics.chartElements = dashboardElements.charts;
    
    console.log('📋 Dashboard Elements Found:');
    console.log(`   - Header: ${dashboardElements.header ? '✅' : '❌'}`);
    console.log(`   - Charts: ${dashboardElements.charts} elements`);
    console.log(`   - Sections: ${dashboardElements.sections} sections`);
    console.log(`   - Buttons: ${dashboardElements.buttons} buttons`);
    console.log(`   - Loading states: ${dashboardElements.loading} elements`);
    
    // Check for React errors
    const reactErrors = await page.evaluate(() => {
      return window.__REACT_DEVTOOLS_GLOBAL_HOOK__?.reactDevtoolsAgent?.errors || [];
    });
    
    if (reactErrors.length > 0) {
      metrics.errors.push(...reactErrors);
    }
    
    // Measure memory usage
    const performanceMetrics = await page.metrics();
    metrics.memoryUsage = Math.round(performanceMetrics.JSHeapUsedSize / 1024 / 1024 * 100) / 100;
    
    console.log(`🧠 Memory Usage: ${metrics.memoryUsage} MB`);
    
    // Take a screenshot for verification
    await page.screenshot({ 
      path: 'performance-dashboard-verification.png',
      fullPage: true 
    });
    
    console.log('📸 Screenshot saved: performance-dashboard-verification.png');
    
    // Check if the page loaded without critical errors
    const hasCriticalErrors = metrics.errors.some(error => 
      error.includes('Cannot read properties of null') ||
      error.includes('type is invalid') ||
      error.includes('undefined')
    );
    
    metrics.success = !hasCriticalErrors && response.status() === 200;
    
    if (metrics.success) {
      console.log('\n🎉 Performance Dashboard Verification PASSED!');
    } else {
      console.log('\n❌ Performance Dashboard Verification FAILED!');
    }
    
  } catch (error) {
    console.error('❌ Error during verification:', error.message);
    metrics.errors.push(error.message);
    metrics.success = false;
  } finally {
    await browser.close();
  }
  
  // Generate report
  const report = {
    timestamp: new Date().toISOString(),
    metrics,
    summary: {
      status: metrics.success ? 'PASSED' : 'FAILED',
      loadTime: `${metrics.loadTime}ms`,
      errorCount: metrics.errors.length,
      chartElements: metrics.chartElements,
      memoryUsage: `${metrics.memoryUsage} MB`
    }
  };
  
  // Save report
  fs.writeFileSync(
    'performance-dashboard-report.json', 
    JSON.stringify(report, null, 2)
  );
  
  console.log('\n📊 Verification Report:');
  console.log(`   Status: ${report.summary.status}`);
  console.log(`   Load Time: ${report.summary.loadTime}`);
  console.log(`   Errors: ${report.summary.errorCount}`);
  console.log(`   Chart Elements: ${report.summary.chartElements}`);
  console.log(`   Memory Usage: ${report.summary.memoryUsage}`);
  
  if (metrics.errors.length > 0) {
    console.log('\n⚠️  Errors Found:');
    metrics.errors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`);
    });
  }
  
  console.log('\n📄 Full report saved: performance-dashboard-report.json');
  
  return metrics.success;
}

// Run verification if called directly
if (require.main === module) {
  verifyPerformanceDashboard()
    .then((success) => {
      process.exit(success ? 0 : 1);
    })
    .catch((error) => {
      console.error('Fatal error:', error);
      process.exit(1);
    });
}

module.exports = { verifyPerformanceDashboard };

#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Optimizing Imports for Better Tree-shaking\n');

// Function to update lucide-react imports
function optimizeLucideImports() {
  console.log('📦 Optimizing Lucide React imports...');
  
  try {
    // Find all files with lucide-react imports
    const result = execSync('grep -r "from.*lucide-react" app/ components/ lib/ hooks/ --include="*.tsx" --include="*.ts" -l', { encoding: 'utf8' });
    const files = result.split('\n').filter(f => f.trim());
    
    console.log(`Found ${files.length} files with lucide-react imports`);
    
    files.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Extract imported icons
        const importMatches = content.match(/import\s*{([^}]+)}\s*from\s*['"`]lucide-react['"`]/g);
        
        if (importMatches) {
          let newContent = content;
          
          importMatches.forEach(importStatement => {
            const iconsMatch = importStatement.match(/import\s*{([^}]+)}/);
            if (iconsMatch) {
              const icons = iconsMatch[1].split(',').map(icon => icon.trim());
              
              // Create individual imports
              const individualImports = icons.map(icon => 
                `import { ${icon} } from 'lucide-react/dist/esm/icons/${icon.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1)}'`
              ).join('\n');
              
              // Replace the original import
              newContent = newContent.replace(importStatement, individualImports);
            }
          });
          
          if (newContent !== content) {
            console.log(`  ✅ Updated: ${file}`);
            // Uncomment the next line to actually apply changes
            // fs.writeFileSync(file, newContent);
          }
        }
      } catch (error) {
        console.log(`  ❌ Error processing ${file}: ${error.message}`);
      }
    });
    
  } catch (error) {
    console.log('No lucide-react imports found or error occurred');
  }
}

// Function to optimize date-fns imports
function optimizeDateFnsImports() {
  console.log('\n📅 Optimizing Date-fns imports...');
  
  try {
    const result = execSync('grep -r "from.*date-fns" app/ components/ lib/ hooks/ --include="*.tsx" --include="*.ts" -l', { encoding: 'utf8' });
    const files = result.split('\n').filter(f => f.trim());
    
    console.log(`Found ${files.length} files with date-fns imports`);
    
    files.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Extract imported functions
        const importMatches = content.match(/import\s*{([^}]+)}\s*from\s*['"`]date-fns['"`]/g);
        
        if (importMatches) {
          let newContent = content;
          
          importMatches.forEach(importStatement => {
            const functionsMatch = importStatement.match(/import\s*{([^}]+)}/);
            if (functionsMatch) {
              const functions = functionsMatch[1].split(',').map(fn => fn.trim());
              
              // Create individual imports
              const individualImports = functions.map(fn => 
                `import { ${fn} } from 'date-fns/${fn}'`
              ).join('\n');
              
              // Replace the original import
              newContent = newContent.replace(importStatement, individualImports);
            }
          });
          
          if (newContent !== content) {
            console.log(`  ✅ Updated: ${file}`);
            // Uncomment the next line to actually apply changes
            // fs.writeFileSync(file, newContent);
          }
        }
      } catch (error) {
        console.log(`  ❌ Error processing ${file}: ${error.message}`);
      }
    });
    
  } catch (error) {
    console.log('No date-fns imports found or error occurred');
  }
}

// Create optimized icon bundle
function createIconBundle() {
  console.log('\n🎨 Creating optimized icon bundle...');
  
  // Extract all used icons
  try {
    const result = execSync('grep -r "from.*lucide-react" app/ components/ lib/ hooks/ --include="*.tsx" --include="*.ts"', { encoding: 'utf8' });
    const lines = result.split('\n').filter(line => line.trim());
    
    const allIcons = new Set();
    lines.forEach(line => {
      const match = line.match(/import\s*{([^}]+)}/);
      if (match) {
        const icons = match[1].split(',').map(icon => icon.trim());
        icons.forEach(icon => allIcons.add(icon));
      }
    });
    
    console.log(`Found ${allIcons.size} unique icons used`);
    
    // Create icon bundle file
    const iconBundleContent = `// Auto-generated optimized icon bundle
// This file exports only the icons used in the application

${Array.from(allIcons).map(icon => 
  `export { ${icon} } from 'lucide-react/dist/esm/icons/${icon.toLowerCase().replace(/([A-Z])/g, '-$1').substring(1)}';`
).join('\n')}
`;
    
    console.log('  📝 Icon bundle content generated');
    console.log('  💡 Save this to lib/icons.ts and import from there instead');
    
    // Show sample of the bundle
    console.log('\n  Sample content:');
    console.log(iconBundleContent.split('\n').slice(0, 5).join('\n'));
    console.log('  ...');
    
  } catch (error) {
    console.log('Error creating icon bundle:', error.message);
  }
}

// Main execution
console.log('⚠️  DRY RUN MODE - No files will be modified');
console.log('   Uncomment the fs.writeFileSync lines to apply changes\n');

optimizeLucideImports();
optimizeDateFnsImports();
createIconBundle();

console.log('\n🎯 Next Steps:');
console.log('1. Review the proposed changes above');
console.log('2. Uncomment fs.writeFileSync lines to apply changes');
console.log('3. Run the script again to apply optimizations');
console.log('4. Test the application to ensure everything works');
console.log('5. Run bundle analysis to verify improvements');

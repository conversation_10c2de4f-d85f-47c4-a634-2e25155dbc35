#!/usr/bin/env node

/**
 * Firefox Optimization Testing Script
 * 
 * This script provides easy commands to test the performance optimizations
 * and verify the improvements in Firefox.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');

console.log('🦊 Firefox Performance Optimization Testing\n');

const commands = {
  'test-components': {
    description: 'Test component size optimizations',
    command: () => {
      console.log('📊 Testing component optimizations...\n');
      
      // Test performance dashboard components
      console.log('✅ Performance Dashboard Components:');
      execSync('find components/dashboard/performance -name "*.tsx" -exec wc -c {} + | sort -n', { stdio: 'inherit' });
      
      console.log('\n✅ Split Analytics Components:');
      execSync('find components/dashboard/affiliate-links/analytics -name "*.tsx" -exec wc -c {} + | sort -n', { stdio: 'inherit' });
      
      console.log('\n📋 Component Analysis Summary:');
      execSync('node scripts/firefox-performance-test.js', { stdio: 'inherit' });
    }
  },
  
  'test-typescript': {
    description: 'Verify TypeScript compilation',
    command: () => {
      console.log('🔧 Testing TypeScript compilation...\n');
      try {
        execSync('bun run tsc --noEmit', { stdio: 'inherit' });
        console.log('\n✅ TypeScript compilation successful - 0 errors!');
      } catch (error) {
        console.log('\n❌ TypeScript compilation failed');
        process.exit(1);
      }
    }
  },
  
  'start-dev': {
    description: 'Start optimized development server',
    command: () => {
      console.log('🚀 Starting optimized development server...\n');
      console.log('Server will start at: http://localhost:3000');
      console.log('Performance dashboard: http://localhost:3000/dashboard/performance');
      console.log('\nPress Ctrl+C to stop the server\n');
      
      const devProcess = spawn('bun', ['run', 'dev:minimal'], { 
        stdio: 'inherit',
        shell: true 
      });
      
      process.on('SIGINT', () => {
        console.log('\n🛑 Stopping development server...');
        devProcess.kill('SIGINT');
        process.exit(0);
      });
    }
  },
  
  'test-firefox': {
    description: 'Open Firefox for performance testing',
    command: () => {
      console.log('🦊 Opening Firefox for performance testing...\n');
      
      console.log('📋 Testing Instructions:');
      console.log('1. Open Firefox DevTools (F12)');
      console.log('2. Go to Performance tab');
      console.log('3. Click "Start Recording Performance"');
      console.log('4. Navigate around the dashboard');
      console.log('5. Stop recording and analyze results');
      console.log('6. Check Memory tab for usage');
      console.log('7. Check Network tab for bundle sizes\n');
      
      try {
        // Try different ways to open Firefox depending on OS
        if (process.platform === 'darwin') {
          execSync('open -a Firefox http://localhost:3000/dashboard/performance');
        } else if (process.platform === 'win32') {
          execSync('start firefox http://localhost:3000/dashboard/performance');
        } else {
          execSync('firefox http://localhost:3000/dashboard/performance &');
        }
        console.log('✅ Firefox opened successfully');
      } catch (error) {
        console.log('⚠️  Could not auto-open Firefox. Please manually navigate to:');
        console.log('   http://localhost:3000/dashboard/performance');
      }
    }
  },
  
  'analyze-bundle': {
    description: 'Run bundle size analysis',
    command: () => {
      console.log('📦 Analyzing bundle size...\n');
      try {
        execSync('bun run analyze:bundle', { stdio: 'inherit' });
      } catch (error) {
        console.log('⚠️  Bundle analysis not available. Building first...');
        execSync('bun run build', { stdio: 'inherit' });
        console.log('\n📊 Bundle built. Run this command again for analysis.');
      }
    }
  },
  
  'full-test': {
    description: 'Run complete optimization test suite',
    command: () => {
      console.log('🧪 Running complete optimization test suite...\n');
      
      // Run all tests in sequence
      console.log('1️⃣ Testing TypeScript compilation...');
      commands['test-typescript'].command();
      
      console.log('\n2️⃣ Testing component optimizations...');
      commands['test-components'].command();
      
      console.log('\n3️⃣ Generating performance report...');
      execSync('node scripts/firefox-performance-test.js', { stdio: 'inherit' });
      
      console.log('\n✅ All tests completed successfully!');
      console.log('\n🚀 Next steps:');
      console.log('   1. Run: npm run test-firefox-optimizations start-dev');
      console.log('   2. Run: npm run test-firefox-optimizations test-firefox');
      console.log('   3. Measure performance in Firefox DevTools');
    }
  }
};

// Parse command line arguments
const command = process.argv[2];

if (!command || command === 'help') {
  console.log('Available commands:\n');
  Object.entries(commands).forEach(([cmd, info]) => {
    console.log(`  ${cmd.padEnd(20)} - ${info.description}`);
  });
  console.log('\nUsage: node scripts/test-firefox-optimizations.js <command>');
  console.log('Example: node scripts/test-firefox-optimizations.js full-test');
  process.exit(0);
}

if (commands[command]) {
  commands[command].command();
} else {
  console.log(`❌ Unknown command: ${command}`);
  console.log('Run "node scripts/test-firefox-optimizations.js help" for available commands');
  process.exit(1);
}

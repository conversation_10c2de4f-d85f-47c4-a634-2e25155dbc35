#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Read package.json
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };

// Function to get directory size
function getDirSize(dirPath) {
  try {
    const stats = fs.statSync(dirPath);
    if (stats.isFile()) {
      return stats.size;
    } else if (stats.isDirectory()) {
      let totalSize = 0;
      const files = fs.readdirSync(dirPath);
      for (const file of files) {
        const filePath = path.join(dirPath, file);
        totalSize += getDirSize(filePath);
      }
      return totalSize;
    }
  } catch (error) {
    return 0;
  }
  return 0;
}

// Analyze dependencies
const dependencyAnalysis = [];

for (const [name, version] of Object.entries(dependencies)) {
  const depPath = path.join('node_modules', name);
  const size = getDirSize(depPath);
  
  if (size > 0) {
    dependencyAnalysis.push({
      name,
      version,
      size,
      sizeMB: (size / 1024 / 1024).toFixed(2)
    });
  }
}

// Sort by size (largest first)
dependencyAnalysis.sort((a, b) => b.size - a.size);

console.log('📦 Dependency Size Analysis\n');
console.log('Top 20 largest dependencies:');
console.log('=' .repeat(60));

dependencyAnalysis.slice(0, 20).forEach((dep, index) => {
  console.log(`${(index + 1).toString().padStart(2)}. ${dep.name.padEnd(30)} ${dep.sizeMB.padStart(8)} MB`);
});

console.log('\n📊 Summary:');
console.log('=' .repeat(60));

const totalSize = dependencyAnalysis.reduce((sum, dep) => sum + dep.size, 0);
const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2);

console.log(`Total dependencies: ${dependencyAnalysis.length}`);
console.log(`Total size: ${totalSizeMB} MB`);

// Identify potential optimizations
console.log('\n🔍 Potential Optimizations:');
console.log('=' .repeat(60));

const heavyDeps = dependencyAnalysis.filter(dep => dep.size > 5 * 1024 * 1024); // > 5MB
const chartDeps = dependencyAnalysis.filter(dep => dep.name.includes('chart') || dep.name.includes('d3') || dep.name.includes('recharts'));
const uiDeps = dependencyAnalysis.filter(dep => dep.name.includes('radix') || dep.name.includes('ui'));

if (heavyDeps.length > 0) {
  console.log('\n🚨 Heavy dependencies (>5MB):');
  heavyDeps.forEach(dep => {
    console.log(`  - ${dep.name}: ${dep.sizeMB} MB`);
  });
}

if (chartDeps.length > 0) {
  console.log('\n📈 Chart-related dependencies:');
  chartDeps.forEach(dep => {
    console.log(`  - ${dep.name}: ${dep.sizeMB} MB`);
  });
  const chartTotalMB = chartDeps.reduce((sum, dep) => sum + dep.size, 0) / 1024 / 1024;
  console.log(`  Total chart dependencies: ${chartTotalMB.toFixed(2)} MB`);
}

if (uiDeps.length > 0) {
  console.log('\n🎨 UI-related dependencies:');
  uiDeps.forEach(dep => {
    console.log(`  - ${dep.name}: ${dep.sizeMB} MB`);
  });
  const uiTotalMB = uiDeps.reduce((sum, dep) => sum + dep.size, 0) / 1024 / 1024;
  console.log(`  Total UI dependencies: ${uiTotalMB.toFixed(2)} MB`);
}

// Check for duplicate or similar dependencies
console.log('\n🔄 Potential Duplicates/Similar:');
const nameGroups = {};
dependencyAnalysis.forEach(dep => {
  const baseName = dep.name.split('-')[0].split('/').pop();
  if (!nameGroups[baseName]) nameGroups[baseName] = [];
  nameGroups[baseName].push(dep);
});

Object.entries(nameGroups).forEach(([baseName, deps]) => {
  if (deps.length > 1) {
    console.log(`  ${baseName}:`);
    deps.forEach(dep => {
      console.log(`    - ${dep.name}: ${dep.sizeMB} MB`);
    });
  }
});

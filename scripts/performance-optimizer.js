#!/usr/bin/env node

/**
 * Performance Optimization Script for Next.js Development
 * 
 * This script provides comprehensive performance optimizations for faster compilation
 * and development experience.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting performance optimization...\n');

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📋',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    optimize: '⚡'
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function runCommand(command, description) {
  try {
    log(`${description}...`, 'optimize');
    execSync(command, { stdio: 'inherit' });
    log(`${description} completed`, 'success');
    return true;
  } catch (error) {
    log(`${description} failed: ${error.message}`, 'error');
    return false;
  }
}

function optimizeTypeScript() {
  log('Optimizing TypeScript configuration...', 'optimize');
  
  // Check if TypeScript errors exist
  try {
    execSync('bun run tsc --noEmit --skipLibCheck', { stdio: 'pipe' });
    log('TypeScript compilation is clean', 'success');
  } catch (error) {
    log('TypeScript errors detected - this may slow compilation', 'warning');
    log('Run "bun run tsc --noEmit" to see errors', 'info');
  }
}

function optimizeCache() {
  log('Optimizing build cache...', 'optimize');
  
  // Clean corrupted cache
  runCommand('node scripts/cache-manager.js clean', 'Cache cleanup');
  
  // Optimize cache configuration
  runCommand('node scripts/cache-manager.js optimize', 'Cache optimization');
}

function optimizeDependencies() {
  log('Analyzing dependencies...', 'optimize');
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const depCount = Object.keys(packageJson.dependencies || {}).length;
  const devDepCount = Object.keys(packageJson.devDependencies || {}).length;
  
  log(`Dependencies: ${depCount} production, ${devDepCount} development`, 'info');
  
  if (depCount > 50) {
    log('Large number of dependencies detected - consider code splitting', 'warning');
  }
}

function optimizeEnvironment() {
  log('Setting up optimized environment...', 'optimize');
  
  // Create .env.local with performance optimizations
  const envContent = `# Performance optimizations
ENABLE_PERFORMANCE_METRICS=false
ENABLE_PERFORMANCE_LOGGING=false
NEXT_TELEMETRY_DISABLED=1
TURBOPACK=1
`;
  
  if (!fs.existsSync('.env.local')) {
    fs.writeFileSync('.env.local', envContent);
    log('Created .env.local with performance settings', 'success');
  } else {
    log('.env.local already exists - check performance settings manually', 'info');
  }
}

function createOptimizedScripts() {
  log('Creating optimized development scripts...', 'optimize');
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Add performance-optimized scripts if they don't exist
  const newScripts = {
    'dev:turbo-fast': 'NODE_OPTIONS="--max-old-space-size=8192" ENABLE_PERFORMANCE_METRICS=false bun --bun run next dev --turbo',
    'dev:production-like': 'NODE_ENV=production bun --bun run next dev --turbo',
    'analyze:performance': 'node scripts/performance-optimizer.js analyze',
    'fix:types': 'bun run tsc --noEmit --skipLibCheck',
  };
  
  let updated = false;
  for (const [script, command] of Object.entries(newScripts)) {
    if (!packageJson.scripts[script]) {
      packageJson.scripts[script] = command;
      updated = true;
    }
  }
  
  if (updated) {
    fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    log('Added optimized development scripts', 'success');
  }
}

function analyzePerformance() {
  log('Analyzing current performance...', 'optimize');
  
  // Check cache size
  runCommand('node scripts/cache-manager.js analyze', 'Cache analysis');
  
  // Check for TypeScript issues
  optimizeTypeScript();
  
  // Analyze dependencies
  optimizeDependencies();
  
  // Performance recommendations
  console.log('\n💡 Performance Recommendations:');
  console.log('1. Use "bun run dev:minimal" for fastest development');
  console.log('2. Use "bun run dev:turbo-fast" for optimized Turbopack');
  console.log('3. Fix TypeScript errors to improve compilation speed');
  console.log('4. Consider lazy loading heavy components');
  console.log('5. Use dynamic imports for large libraries');
  console.log('6. Monitor bundle size with "bun run debug:bundle"');
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'optimize';
  
  switch (command) {
    case 'optimize':
      optimizeCache();
      optimizeEnvironment();
      createOptimizedScripts();
      optimizeTypeScript();
      log('Performance optimization complete!', 'success');
      console.log('\n🎯 Next steps:');
      console.log('1. Run "bun run dev:minimal" for fastest development');
      console.log('2. Fix any TypeScript errors for better performance');
      console.log('3. Use "bun run analyze:performance" to monitor performance');
      break;
      
    case 'analyze':
      analyzePerformance();
      break;
      
    case 'clean':
      optimizeCache();
      break;
      
    default:
      console.log(`
Usage: node scripts/performance-optimizer.js <command>

Commands:
  optimize  - Run full performance optimization
  analyze   - Analyze current performance
  clean     - Clean and optimize cache

Examples:
  node scripts/performance-optimizer.js optimize
  node scripts/performance-optimizer.js analyze
      `);
  }
}

if (require.main === module) {
  main();
}

module.exports = { optimizeCache, optimizeTypeScript, analyzePerformance };

import type { Metadata } from "next"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { AnalyticsHeader } from "@/components/dashboard/analytics/analytics-header"
import { AnalyticsOverview } from "@/components/dashboard/analytics/analytics-overview"
import { EngagementMetrics } from "@/components/dashboard/analytics/engagement-metrics"
import { AudienceDemographics } from "@/components/dashboard/analytics/audience-demographics"
import { ContentPerformance } from "@/components/dashboard/analytics/content-performance"
import { PlatformComparison } from "@/components/dashboard/analytics/platform-comparison"

export const metadata: Metadata = {
  title: "Analytics | Social Media Dashboard",
  description: "Comprehensive analytics for your social media performance",
}

export default function AnalyticsPage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <AnalyticsHeader />
        <AnalyticsOverview />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <EngagementMetrics />
          <AudienceDemographics />
        </div>
        <ContentPerformance />
        <PlatformComparison />
      </div>
    </DashboardLayout>
  )
}

import type { Metadata } from "next"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { EngagementHeader } from "@/components/dashboard/engagement/engagement-header"
import { EngagementOverview } from "@/components/dashboard/engagement/engagement-overview"
import { EngagementTrends } from "@/components/dashboard/engagement/engagement-trends"
import { EngagementByPlatform } from "@/components/dashboard/engagement/engagement-by-platform"
import { EngagementByContent } from "@/components/dashboard/engagement/engagement-by-content"
import { EngagementHeatmap } from "@/components/dashboard/engagement/engagement-heatmap"
import { EngagementInsights } from "@/components/dashboard/engagement/engagement-insights"

export const metadata: Metadata = {
  title: "Engagement | Social Media Dashboard",
  description: "Detailed engagement analytics and interaction insights",
}

export default function EngagementPage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <EngagementHeader title="Engagement Analytics" />
        <EngagementOverview />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <EngagementTrends />
          <EngagementByPlatform />
        </div>
        <EngagementByContent />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <EngagementHeatmap />
          <EngagementInsights />
        </div>
      </div>
    </DashboardLayout>
  )
}

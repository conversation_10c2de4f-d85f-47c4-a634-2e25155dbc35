import type { Metadata } from "next"
import { Suspense } from "react"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { CampaignsHeader } from "@/components/dashboard/campaigns/campaigns-header"
import { CampaignMetrics } from "@/components/dashboard/campaigns/campaign-metrics"
import { CampaignsList } from "@/components/dashboard/campaigns/campaigns-list"
import { CampaignDetails } from "@/components/dashboard/campaigns/campaign-details"
import { FirefoxPerformanceMonitor } from "@/components/debug/FirefoxPerformanceMonitor"

// Loading components for better UX
function MetricsLoading() {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="h-32 bg-muted animate-pulse rounded-lg" />
      ))}
    </div>
  )
}

function CampaignsListLoading() {
  return <div className="h-96 bg-muted animate-pulse rounded-lg" />
}

function CampaignDetailsLoading() {
  return <div className="h-96 bg-muted animate-pulse rounded-lg" />
}

export const metadata: Metadata = {
  title: "Campaigns | Social Media Dashboard",
  description: "Manage and analyze your social media campaigns",
}

export default function CampaignsPage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <CampaignsHeader />

        <Suspense fallback={<MetricsLoading />}>
          <CampaignMetrics />
        </Suspense>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <Suspense fallback={<CampaignsListLoading />}>
              <CampaignsList />
            </Suspense>
          </div>
          <div>
            <Suspense fallback={<CampaignDetailsLoading />}>
              <CampaignDetails />
            </Suspense>
          </div>
        </div>
      </div>

      {/* Performance monitor for Firefox debugging */}
      <FirefoxPerformanceMonitor />
    </DashboardLayout>
  )
}

import type { Metadata } from "next"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { ExportHeader } from "@/components/dashboard/export/export-header"
import { ExportOptions } from "@/components/dashboard/export/export-options"
import { ExportHistory } from "@/components/dashboard/export/export-history"
import { ScheduledExports } from "@/components/dashboard/export/scheduled-exports"

export const metadata: Metadata = {
  title: "Export Data | SocialPulse Dashboard",
  description: "Export your social media analytics data in various formats",
}

export default function ExportPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <ExportHeader />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <div className="col-span-4">
            <ExportOptions />
          </div>
          <div className="col-span-3">
            <ScheduledExports />
          </div>
        </div>
        <ExportHistory />
      </div>
    </DashboardLayout>
  )
}

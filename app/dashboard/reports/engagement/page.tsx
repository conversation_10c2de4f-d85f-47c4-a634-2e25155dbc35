import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { EngagementHeader } from "@/components/dashboard/engagement/engagement-header"
import { EngagementOverview } from "@/components/dashboard/engagement/engagement-overview"
import { EngagementTrends } from "@/components/dashboard/engagement/engagement-trends"
import { EngagementByPlatform } from "@/components/dashboard/engagement/engagement-by-platform"
import { EngagementByContent } from "@/components/dashboard/engagement/engagement-by-content"
import { EngagementHeatmap } from "@/components/dashboard/engagement/engagement-heatmap"
import { EngagementInsights } from "@/components/dashboard/engagement/engagement-insights"

export const metadata = {
  title: "Engagement Analytics | Social Media Dashboard",
  description: "Detailed engagement metrics and interaction analysis for your social media content",
}

export default function EngagementPage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6 p-6">
        <EngagementHeader title="Engagement Report" />
        <EngagementOverview />
        <div className="grid gap-6 lg:grid-cols-2">
          <EngagementTrends />
          <EngagementByPlatform />
        </div>
        <EngagementByContent />
        <div className="grid gap-6 lg:grid-cols-2">
          <EngagementHeatmap />
          <EngagementInsights />
        </div>
      </div>
    </DashboardLayout>
  )
}

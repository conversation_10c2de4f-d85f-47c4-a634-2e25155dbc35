import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { AudienceHeader } from "@/components/dashboard/audience/audience-header"
import { AudienceOverview } from "@/components/dashboard/audience/audience-overview"
import { AudienceGrowth } from "@/components/dashboard/audience/audience-growth"
import { AudienceDemographics } from "@/components/dashboard/audience/audience-demographics"
import { AudienceActivity } from "@/components/dashboard/audience/audience-activity"
import { AudienceInterests } from "@/components/dashboard/audience/audience-interests"
import { AudienceSegments } from "@/components/dashboard/audience/audience-segments"

export const metadata = {
  title: "Audience Analytics | Social Media Dashboard",
  description: "Deep audience insights and demographic analysis for your social media presence",
}

export default function AudiencePage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6 p-6">
        <AudienceHeader />
        <AudienceOverview />
        <div className="grid gap-6 lg:grid-cols-2">
          <AudienceGrowth />
          <AudienceActivity />
        </div>
        <AudienceDemographics />
        <div className="grid gap-6 lg:grid-cols-2">
          <AudienceInterests />
          <AudienceSegments />
        </div>
      </div>
    </DashboardLayout>
  )
}

import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { PerformanceHeader } from "@/components/dashboard/performance/performance-header"
import { PerformanceOverview } from "@/components/dashboard/performance/performance-overview"
import { PerformanceTrends } from "@/components/dashboard/performance/performance-trends"
import { PerformanceGoals } from "@/components/dashboard/performance/performance-goals"
import { TopPerformingContent } from "@/components/dashboard/performance/top-performing-content"
import { PlatformPerformance } from "@/components/dashboard/performance/platform-performance"
import { PerformanceInsights } from "@/components/dashboard/performance/performance-insights"

export const metadata = {
  title: "Performance Analytics | Social Media Dashboard",
  description: "Comprehensive performance analytics and insights for your social media campaigns",
}

export default function PerformancePage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6 p-6">
        <PerformanceHeader />
        <PerformanceOverview />
        <div className="grid gap-6 lg:grid-cols-2">
          <PerformanceTrends />
          <PerformanceGoals />
        </div>
        <TopPerformingContent />
        <div className="grid gap-6 lg:grid-cols-2">
          <PlatformPerformance />
          <PerformanceInsights />
        </div>
      </div>
    </DashboardLayout>
  )
}

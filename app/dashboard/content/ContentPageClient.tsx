"use client"

import { useState } from "react"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { ContentHeader } from "@/components/dashboard/content/content-header"
import { ContentAnalytics } from "@/components/dashboard/content/content-analytics"
import { ContentLibrary } from "@/components/dashboard/content/content-library"
import { ContentDetails } from "@/components/dashboard/content/content-details"

export default function ContentPageClient() {
  const [selectedContent, setSelectedContent] = useState(null)

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <ContentHeader />
        <ContentAnalytics />
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <ContentLibrary onSelectContent={setSelectedContent} />
          </div>
          <div>
            <ContentDetails content={selectedContent || undefined} />
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

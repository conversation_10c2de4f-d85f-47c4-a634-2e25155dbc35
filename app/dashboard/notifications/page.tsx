import type { <PERSON>ada<PERSON> } from "next"
import { DashboardLayout } from "@/components/dashboard/dashboard-layout"
import { NotificationsHeader } from "@/components/dashboard/notifications/notifications-header"
import { NotificationSettings } from "@/components/dashboard/notifications/notification-settings"
import { NotificationHistory } from "@/components/dashboard/notifications/notification-history"
import { LiveNotifications } from "@/components/dashboard/notifications/live-notifications"

export const metadata: Metadata = {
  title: "Notifications | SocialPulse Dashboard",
  description: "Manage your notification preferences and view notification history",
}

export default function NotificationsPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <NotificationsHeader />
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-7">
          <div className="col-span-4">
            <NotificationHistory />
          </div>
          <div className="col-span-3 space-y-6">
            <LiveNotifications />
            <NotificationSettings />
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}

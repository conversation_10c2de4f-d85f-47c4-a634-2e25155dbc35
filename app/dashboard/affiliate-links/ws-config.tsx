"use client"
import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Settings } from "lucide-react"

export function WebSocketConfig() {
  const [wsUrl, setWsUrl] = useState(process.env.NEXT_PUBLIC_ANALYTICS_WS_URL || "wss://analytics-api.example.com/ws")
  const [apiKey, setApiKey] = useState("")
  const [reconnectAttempts, setReconnectAttempts] = useState("5")
  const [reconnectInterval, setReconnectInterval] = useState("3000")

  const handleSave = () => {
    // In a real application, this might store these in localStorage or similar
    // For demo purposes, we just close the dialog
    console.log("WebSocket configuration saved:", {
      wsUrl,
      apiKey,
      reconnectAttempts,
      reconnectInterval,
    })

    // In a real app, you might update a context or trigger a refresh
    window.localStorage.setItem(
      "ws_config",
      JSON.stringify({
        wsUrl,
        apiKey,
        reconnectAttempts: Number.parseInt(reconnectAttempts),
        reconnectInterval: Number.parseInt(reconnectInterval),
      }),
    )

    // Display notification or trigger re-connection
    alert("WebSocket configuration saved. Please refresh the page for changes to take effect.")
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          Configure WebSocket
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>WebSocket Configuration</DialogTitle>
          <DialogDescription>Configure the WebSocket connection for real-time analytics data.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="wsUrl" className="text-right">
              WebSocket URL
            </Label>
            <Input id="wsUrl" value={wsUrl} onChange={(e) => setWsUrl(e.target.value)} className="col-span-3" />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="apiKey" className="text-right">
              API Key
            </Label>
            <Input
              id="apiKey"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              className="col-span-3"
              type="password"
              placeholder="Leave blank to use current API key"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reconnectAttempts" className="text-right">
              Reconnect Attempts
            </Label>
            <Input
              id="reconnectAttempts"
              value={reconnectAttempts}
              onChange={(e) => setReconnectAttempts(e.target.value)}
              className="col-span-3"
              type="number"
              min="1"
              max="20"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reconnectInterval" className="text-right">
              Reconnect Interval (ms)
            </Label>
            <Input
              id="reconnectInterval"
              value={reconnectInterval}
              onChange={(e) => setReconnectInterval(e.target.value)}
              className="col-span-3"
              type="number"
              min="1000"
              max="30000"
              step="1000"
            />
          </div>
        </div>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm">WebSocket Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs space-y-1">
              <div className="flex justify-between">
                <span>Current Configuration:</span>
                <span className="font-mono">{wsUrl}</span>
              </div>
              <div className="flex justify-between">
                <span>Protocol:</span>
                <span className="font-mono">{wsUrl.startsWith("wss") ? "WSS (Secure)" : "WS"}</span>
              </div>
              <div className="flex justify-between">
                <span>Authentication:</span>
                <span className="font-mono">{apiKey ? "API Key" : "None"}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end gap-3">
          <DialogTrigger asChild>
            <Button variant="outline">Cancel</Button>
          </DialogTrigger>
          <Button onClick={handleSave}>Save Configuration</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}

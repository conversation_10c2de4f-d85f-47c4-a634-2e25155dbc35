"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Bell, BellRing, X } from "lucide-react"

interface LiveNotification {
  id: string
  title: string
  message: string
  type: "info" | "success" | "warning" | "error"
  timestamp: Date
}

export function LiveNotifications() {
  const [notifications, setNotifications] = useState<LiveNotification[]>([])
  const [isEnabled, setIsEnabled] = useState(true)

  // Simulate real-time notifications
  useEffect(() => {
    if (!isEnabled) return

    const interval = setInterval(() => {
      const mockNotifications = [
        {
          title: "New Click Detected",
          message: "Affiliate link 'summer-sale' received a new click",
          type: "info" as const,
        },
        {
          title: "Conversion Alert",
          message: "New conversion from Instagram campaign",
          type: "success" as const,
        },
        {
          title: "Budget Warning",
          message: "Campaign budget 90% depleted",
          type: "warning" as const,
        },
      ]

      const randomNotification = mockNotifications[Math.floor(Math.random() * mockNotifications.length)]
      const newNotification: LiveNotification = {
        id: Date.now().toString(),
        ...randomNotification,
        timestamp: new Date(),
      }

      setNotifications((prev) => [newNotification, ...prev.slice(0, 4)])
    }, 10000) // Every 10 seconds

    return () => clearInterval(interval)
  }, [isEnabled])

  const dismissNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id))
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <BellRing className="h-5 w-5" />
              <span>Live Notifications</span>
            </CardTitle>
            <CardDescription>Real-time alerts and updates</CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={() => setIsEnabled(!isEnabled)}>
            <Bell className={`h-4 w-4 ${isEnabled ? "text-blue-600" : "text-muted-foreground"}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {notifications.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No new notifications</p>
          </div>
        ) : (
          <div className="space-y-3">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className="flex items-start justify-between space-x-3 p-3 bg-muted/50 rounded-lg"
              >
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium">{notification.title}</p>
                    <Badge
                      variant={
                        notification.type === "error"
                          ? "destructive"
                          : notification.type === "warning"
                            ? "default"
                            : notification.type === "success"
                              ? "default"
                              : "secondary"
                      }
                      className="text-xs"
                    >
                      {notification.type}
                    </Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{notification.message}</p>
                  <p className="text-xs text-muted-foreground mt-1">{notification.timestamp.toLocaleTimeString()}</p>
                </div>
                <Button variant="ghost" size="sm" onClick={() => dismissNotification(notification.id)}>
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

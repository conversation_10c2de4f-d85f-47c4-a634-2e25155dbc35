"use client"

import { <PERSON><PERSON><PERSON>, ArrowUp, Heart, MessageSquare, Share2, Repeat, ThumbsUp, Eye } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function EngagementOverview() {
  const metrics = [
    {
      title: "Total Engagements",
      value: "89.2K",
      change: "+15.3%",
      trend: "up",
      icon: Heart,
      description: "All interactions this period",
    },
    {
      title: "Engagement Rate",
      value: "4.2%",
      change: "+0.8%",
      trend: "up",
      icon: Eye,
      description: "Engagements per impression",
    },
    {
      title: "Comments",
      value: "12.4K",
      change: "+22.1%",
      trend: "up",
      icon: MessageSquare,
      description: "Total comments received",
    },
    {
      title: "Shares",
      value: "8.9K",
      change: "+18.7%",
      trend: "up",
      icon: Share2,
      description: "Content shared by users",
    },
    {
      title: "Likes",
      value: "56.8K",
      change: "+12.4%",
      trend: "up",
      icon: ThumbsUp,
      description: "Total likes across platforms",
    },
    {
      title: "Saves/Bookmarks",
      value: "11.1K",
      change: "+28.9%",
      trend: "up",
      icon: Repeat,
      description: "Content saved by users",
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="flex items-center text-xs">
              {metric.trend === "up" ? (
                <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-rose-500" />
              )}
              <span className={metric.trend === "up" ? "text-emerald-500" : "text-rose-500"}>{metric.change}</span>
              <span className="ml-1 text-muted-foreground">vs last period</span>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">{metric.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

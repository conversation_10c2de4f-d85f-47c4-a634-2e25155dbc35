"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TrendingUp, AlertTriangle, CheckCircle, ArrowRight, Clock, Users, Target } from "lucide-react"

export function EngagementInsights() {
  const insights = [
    {
      type: "opportunity",
      title: "Video Content Drives 3x More Engagement",
      description:
        "Your video posts receive 300% more engagement than static images. Consider increasing video content production.",
      impact: "high",
      action: "Create video content strategy",
      icon: TrendingUp,
      metric: "+300% engagement",
    },
    {
      type: "timing",
      title: "Post at 7 PM for Maximum Reach",
      description:
        "Your audience is most active between 6-9 PM on weekdays. Scheduling posts during this window increases engagement by 45%.",
      impact: "medium",
      action: "Adjust posting schedule",
      icon: Clock,
      metric: "+45% engagement",
    },
    {
      type: "audience",
      title: "Questions Drive Comment Engagement",
      description:
        "Posts with questions in captions receive 85% more comments. Your audience loves to share their opinions.",
      impact: "medium",
      action: "Add questions to captions",
      icon: Users,
      metric: "+85% comments",
    },
    {
      type: "success",
      title: "Instagram Stories Performing Exceptionally",
      description: "Your Instagram Stories have a 12% engagement rate, which is 4x higher than the industry average.",
      impact: "high",
      action: "Maintain story strategy",
      icon: CheckCircle,
      metric: "12% engagement rate",
    },
    {
      type: "alert",
      title: "LinkedIn Engagement Declining",
      description:
        "LinkedIn engagement has dropped 20% over the past month. Consider refreshing your professional content strategy.",
      impact: "medium",
      action: "Review LinkedIn strategy",
      icon: AlertTriangle,
      metric: "-20% engagement",
    },
    {
      type: "tip",
      title: "Carousel Posts Generate More Saves",
      description:
        "Carousel posts receive 40% more saves than single-image posts, indicating higher content value perception.",
      impact: "medium",
      action: "Create more carousel content",
      icon: Target,
      metric: "+40% saves",
    },
  ]

  const getInsightColor = (type: string) => {
    switch (type) {
      case "opportunity":
        return "border-l-blue-500 bg-blue-50"
      case "timing":
        return "border-l-purple-500 bg-purple-50"
      case "audience":
        return "border-l-green-500 bg-green-50"
      case "alert":
        return "border-l-orange-500 bg-orange-50"
      case "success":
        return "border-l-emerald-500 bg-emerald-50"
      case "tip":
        return "border-l-indigo-500 bg-indigo-50"
      default:
        return "border-l-gray-500 bg-gray-50"
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "high":
        return "bg-red-100 text-red-800"
      case "medium":
        return "bg-orange-100 text-orange-800"
      case "low":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Engagement Insights</CardTitle>
        <CardDescription>AI-powered recommendations to boost your engagement</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {insights.map((insight, index) => (
            <div key={index} className={`rounded-lg border-l-4 p-4 ${getInsightColor(insight.type)}`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3">
                  <insight.icon className="mt-0.5 h-5 w-5" />
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium">{insight.title}</h4>
                      <Badge variant="outline" className={getImpactColor(insight.impact)}>
                        {insight.impact} impact
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{insight.description}</p>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="bg-white">
                        {insight.metric}
                      </Badge>
                      <Button variant="outline" size="sm">
                        {insight.action}
                        <ArrowRight className="ml-2 h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

"use client"

import { useState, memo, Suspense } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChartProvider, RechartsComponents, ChartSkeleton } from "@/lib/chart-provider"
import { ChartTooltip } from "@/components/ui/chart"

const engagementData = [
  { date: "Jan 1", likes: 4200, comments: 890, shares: 450, saves: 320 },
  { date: "Jan 8", likes: 4800, comments: 1120, shares: 580, saves: 410 },
  { date: "Jan 15", likes: 5200, comments: 980, shares: 520, saves: 380 },
  { date: "Jan 22", likes: 6100, comments: 1340, shares: 720, saves: 520 },
  { date: "Jan 29", likes: 5800, comments: 1250, shares: 680, saves: 490 },
  { date: "Feb 5", likes: 6700, comments: 1580, shares: 820, saves: 610 },
  { date: "Feb 12", likes: 7200, comments: 1720, shares: 890, saves: 680 },
  { date: "Feb 19", likes: 6900, comments: 1650, shares: 850, saves: 640 },
  { date: "Feb 26", likes: 7800, comments: 1890, shares: 980, saves: 750 },
  { date: "Mar 5", likes: 8200, comments: 2010, shares: 1050, saves: 820 },
]

// Optimized chart components using centralized provider
const EngagementOverviewChart = memo(function EngagementOverviewChart({ data }: { data: any[] }) {
  return (
    <ChartProvider>
      <RechartsComponents>
        {(components: any) => (
          <components.ResponsiveContainer width="100%" height={300}>
            <components.AreaChart data={data}>
              <components.CartesianGrid strokeDasharray="3 3" />
              <components.XAxis dataKey="date" />
              <components.YAxis />
              <components.Tooltip content={<ChartTooltip />} />
              <components.Legend />
              <components.Area type="monotone" dataKey="likes" stackId="1" stroke="#8884d8" fill="#8884d8" fillOpacity={0.8} />
              <components.Area type="monotone" dataKey="comments" stackId="1" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.8} />
              <components.Area type="monotone" dataKey="shares" stackId="1" stroke="#ffc658" fill="#ffc658" fillOpacity={0.8} />
              <components.Area type="monotone" dataKey="saves" stackId="1" stroke="#ff8042" fill="#ff8042" fillOpacity={0.8} />
            </components.AreaChart>
          </components.ResponsiveContainer>
        )}
      </RechartsComponents>
    </ChartProvider>
  )
})

const LikesChart = memo(function LikesChart({ data }: { data: any[] }) {
  return (
    <ChartProvider>
      <RechartsComponents>
        {(components: any) => (
          <components.ResponsiveContainer width="100%" height={300}>
            <components.LineChart data={data}>
              <components.CartesianGrid strokeDasharray="3 3" />
              <components.XAxis dataKey="date" />
              <components.YAxis />
              <components.Tooltip content={<ChartTooltip />} />
              <components.Line type="monotone" dataKey="likes" stroke="#8884d8" strokeWidth={3} />
            </components.LineChart>
          </components.ResponsiveContainer>
        )}
      </RechartsComponents>
    </ChartProvider>
  )
})

const CommentsChart = memo(function CommentsChart({ data }: { data: any[] }) {
  return (
    <ChartProvider>
      <RechartsComponents>
        {(components: any) => (
          <components.ResponsiveContainer width="100%" height={300}>
            <components.BarChart data={data}>
              <components.CartesianGrid strokeDasharray="3 3" />
              <components.XAxis dataKey="date" />
              <components.YAxis />
              <components.Tooltip content={<ChartTooltip />} />
              <components.Bar dataKey="comments" fill="#82ca9d" radius={[4, 4, 0, 0]} />
            </components.BarChart>
          </components.ResponsiveContainer>
        )}
      </RechartsComponents>
    </ChartProvider>
  )
})

const SharesChart = memo(function SharesChart({ data }: { data: any[] }) {
  return (
    <ChartProvider>
      <RechartsComponents>
        {(components: any) => (
          <components.ResponsiveContainer width="100%" height={300}>
            <components.LineChart data={data}>
              <components.CartesianGrid strokeDasharray="3 3" />
              <components.XAxis dataKey="date" />
              <components.YAxis />
              <components.Tooltip content={<ChartTooltip />} />
              <components.Line type="monotone" dataKey="shares" stroke="#ffc658" strokeWidth={3} />
            </components.LineChart>
          </components.ResponsiveContainer>
        )}
      </RechartsComponents>
    </ChartProvider>
  )
})

export const EngagementTrends = memo(function EngagementTrends() {
  const [timeframe, setTimeframe] = useState("3months")
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Engagement Trends</CardTitle>
            <CardDescription>Track engagement patterns over time</CardDescription>
          </div>
          <Select value={timeframe} onValueChange={setTimeframe}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1month">Last Month</SelectItem>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="likes">Likes</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
            <TabsTrigger value="shares">Shares</TabsTrigger>
          </TabsList>
          <TabsContent value="overview">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <EngagementOverviewChart data={engagementData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="likes">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <LikesChart data={engagementData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="comments">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <CommentsChart data={engagementData} />
            </Suspense>
          </TabsContent>
          <TabsContent value="shares">
            <Suspense fallback={<ChartSkeleton height={300} />}>
              <SharesChart data={engagementData} />
            </Suspense>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
})

"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Heart, MessageSquare, Share2, Bookmark, ExternalLink } from "lucide-react"

const contentData = [
  {
    id: 1,
    title: "5 Social Media Trends for 2024",
    type: "Video",
    platform: "Instagram",
    date: "2024-01-15",
    engagementRate: 8.2,
    likes: 8900,
    comments: 456,
    shares: 1200,
    saves: 890,
    totalEngagements: 11446,
  },
  {
    id: 2,
    title: "Behind the Scenes: Product Development",
    type: "Story",
    platform: "Instagram",
    date: "2024-01-20",
    engagementRate: 7.8,
    likes: 6200,
    comments: 234,
    shares: 890,
    saves: 620,
    totalEngagements: 7944,
  },
  {
    id: 3,
    title: "Customer Success Spotlight",
    type: "Post",
    platform: "LinkedIn",
    date: "2024-01-22",
    engagementRate: 6.5,
    likes: 3400,
    comments: 189,
    shares: 567,
    saves: 234,
    totalEngagements: 4390,
  },
  {
    id: 4,
    title: "Industry Report: Q1 Insights",
    type: "Article",
    platform: "LinkedIn",
    date: "2024-01-25",
    engagementRate: 5.9,
    likes: 2800,
    comments: 145,
    shares: 890,
    saves: 456,
    totalEngagements: 4291,
  },
  {
    id: 5,
    title: "Quick Tips Tuesday",
    type: "Carousel",
    platform: "Instagram",
    date: "2024-01-28",
    engagementRate: 7.1,
    likes: 3200,
    comments: 98,
    shares: 445,
    saves: 320,
    totalEngagements: 4063,
  },
]

export function EngagementByContent() {
  const [sortBy, setSortBy] = useState("engagementRate")
  const [contentType, setContentType] = useState("all")

  const filteredContent = contentData.filter(
    (content) => contentType === "all" || content.type.toLowerCase() === contentType,
  )

  const sortedContent = [...filteredContent].sort((a, b) => {
    if (sortBy === "engagementRate") return b.engagementRate - a.engagementRate
    if (sortBy === "totalEngagements") return b.totalEngagements - a.totalEngagements
    if (sortBy === "likes") return b.likes - a.likes
    return 0
  })

  const getEngagementColor = (rate: number) => {
    if (rate >= 7) return "bg-emerald-500"
    if (rate >= 5) return "bg-blue-500"
    if (rate >= 3) return "bg-orange-500"
    return "bg-red-500"
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div>
            <CardTitle>Engagement by Content</CardTitle>
            <CardDescription>Analyze engagement metrics for individual content pieces</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={contentType} onValueChange={setContentType}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="video">Video</SelectItem>
                <SelectItem value="post">Post</SelectItem>
                <SelectItem value="story">Story</SelectItem>
                <SelectItem value="carousel">Carousel</SelectItem>
                <SelectItem value="article">Article</SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-[160px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="engagementRate">Engagement Rate</SelectItem>
                <SelectItem value="totalEngagements">Total Engagements</SelectItem>
                <SelectItem value="likes">Likes</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Content</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Engagement Rate</TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Heart className="mr-1 h-4 w-4" />
                    Likes
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <MessageSquare className="mr-1 h-4 w-4" />
                    Comments
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Share2 className="mr-1 h-4 w-4" />
                    Shares
                  </div>
                </TableHead>
                <TableHead className="text-right">
                  <div className="flex items-center justify-end">
                    <Bookmark className="mr-1 h-4 w-4" />
                    Saves
                  </div>
                </TableHead>
                <TableHead className="text-right">Total</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedContent.map((content) => (
                <TableRow key={content.id}>
                  <TableCell>
                    <div className="font-medium">{content.title}</div>
                    <div className="text-sm text-muted-foreground">
                      <Badge variant="outline" className="mr-1">
                        {content.type}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{content.platform}</TableCell>
                  <TableCell>{content.date}</TableCell>
                  <TableCell className="text-right">
                    <Badge className={`${getEngagementColor(content.engagementRate)} text-white`}>
                      {content.engagementRate}%
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">{content.likes.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.comments.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.shares.toLocaleString()}</TableCell>
                  <TableCell className="text-right">{content.saves.toLocaleString()}</TableCell>
                  <TableCell className="text-right font-medium">{content.totalEngagements.toLocaleString()}</TableCell>
                  <TableCell>
                    <Button variant="ghost" size="sm">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}

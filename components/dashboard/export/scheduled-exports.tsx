import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Clock, MoreHorizontal, Pause, Play, Trash2 } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const scheduledExports = [
  {
    id: 1,
    name: "Weekly Analytics Report",
    schedule: "Every Monday at 9:00 AM",
    format: "PDF",
    status: "active",
    nextRun: "Dec 30, 2024",
  },
  {
    id: 2,
    name: "Monthly Campaign Data",
    schedule: "1st of every month",
    format: "Excel",
    status: "active",
    nextRun: "Jan 1, 2025",
  },
  {
    id: 3,
    name: "Daily Affiliate Links",
    schedule: "Daily at 6:00 PM",
    format: "CSV",
    status: "paused",
    nextRun: "Paused",
  },
]

export function ScheduledExports() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Scheduled Exports</CardTitle>
        <CardDescription>Manage your automated export schedules</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {scheduledExports.map((export_) => (
            <div key={export_.id} className="flex items-center justify-between space-x-4">
              <div className="min-w-0 flex-1">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium truncate">{export_.name}</p>
                  <Badge variant={export_.status === "active" ? "default" : "secondary"}>{export_.status}</Badge>
                </div>
                <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  <span>{export_.schedule}</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Next: {export_.nextRun} • {export_.format}
                </p>
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    {export_.status === "active" ? (
                      <>
                        <Pause className="mr-2 h-4 w-4" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="mr-2 h-4 w-4" />
                        Resume
                      </>
                    )}
                  </DropdownMenuItem>
                  <DropdownMenuItem>Edit</DropdownMenuItem>
                  <DropdownMenuItem className="text-destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          ))}
        </div>
        <Button variant="outline" className="w-full mt-4">
          <Clock className="mr-2 h-4 w-4" />
          Create Schedule
        </Button>
      </CardContent>
    </Card>
  )
}

import { <PERSON>, CardContent, CardDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Download, FileText, Calendar, User } from "lucide-react"

const exportHistory = [
  {
    id: 1,
    name: "Analytics Report - December 2024",
    type: "Analytics Data",
    format: "PDF",
    size: "2.4 MB",
    status: "completed",
    createdAt: "Dec 28, 2024 10:30 AM",
    createdBy: "<PERSON>",
    downloadUrl: "#",
  },
  {
    id: 2,
    name: "Campaign Performance Q4",
    type: "Campaign Data",
    format: "Excel",
    size: "1.8 MB",
    status: "completed",
    createdAt: "Dec 27, 2024 3:15 PM",
    createdBy: "<PERSON>",
    downloadUrl: "#",
  },
  {
    id: 3,
    name: "Content Analytics - Weekly",
    type: "Content Data",
    format: "CSV",
    size: "856 KB",
    status: "processing",
    createdAt: "Dec 28, 2024 11:45 AM",
    createdBy: "<PERSON>",
    downloadUrl: null,
  },
  {
    id: 4,
    name: "Audience Demographics",
    type: "Audience Data",
    format: "JSON",
    size: "1.2 MB",
    status: "completed",
    createdAt: "Dec 26, 2024 9:20 AM",
    createdBy: "Mike <PERSON>",
    downloadUrl: "#",
  },
  {
    id: 5,
    name: "Affiliate Links Performance",
    type: "Affiliate Links",
    format: "CSV",
    size: "645 KB",
    status: "failed",
    createdAt: "Dec 25, 2024 2:10 PM",
    createdBy: "Sarah Miller",
    downloadUrl: null,
  },
]

export function ExportHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Export History</CardTitle>
        <CardDescription>View and download your recent exports</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {exportHistory.map((export_) => (
            <div key={export_.id} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <FileText className="h-8 w-8 text-muted-foreground" />
                </div>
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium truncate">{export_.name}</p>
                    <Badge
                      variant={
                        export_.status === "completed"
                          ? "default"
                          : export_.status === "processing"
                            ? "secondary"
                            : "destructive"
                      }
                    >
                      {export_.status}
                    </Badge>
                  </div>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                    <span>{export_.type}</span>
                    <span>•</span>
                    <span>{export_.format}</span>
                    <span>•</span>
                    <span>{export_.size}</span>
                  </div>
                  <div className="flex items-center space-x-4 text-xs text-muted-foreground mt-1">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-3 w-3" />
                      <span>{export_.createdAt}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <User className="h-3 w-3" />
                      <span>{export_.createdBy}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {export_.status === "completed" && export_.downloadUrl && (
                  <Button size="sm" variant="outline">
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                )}
                {export_.status === "failed" && (
                  <Button size="sm" variant="outline">
                    Retry
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

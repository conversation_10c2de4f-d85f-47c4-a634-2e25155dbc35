"use client"

import type React from "react"

import { useState } from "react"
import {
  AlertCircle,
  CheckCircle2,
  Facebook,
  Instagram,
  Linkedin,
  RefreshCw,
  Search,
  Twitter,
  Youtube,
} from "lucide-react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { PlatformConnectionForm } from "./platform-connection-form"
import { toast } from "@/components/ui/use-toast"

// Platform configuration types
type ConnectionStatus = "connected" | "disconnected" | "error" | "pending"

interface PlatformConfig {
  id: string
  name: string
  icon: React.ElementType
  status: ConnectionStatus
  lastSynced?: string
  fields: {
    id: string
    label: string
    type: "text" | "password"
    placeholder: string
    required: boolean
  }[]
  description: string
  docsUrl: string
}

// Platform configurations
const platforms: PlatformConfig[] = [
  {
    id: "facebook",
    name: "Facebook",
    icon: Facebook,
    status: "disconnected",
    fields: [
      {
        id: "app_id",
        label: "App ID",
        type: "text",
        placeholder: "Enter your Facebook App ID",
        required: true,
      },
      {
        id: "app_secret",
        label: "App Secret",
        type: "password",
        placeholder: "Enter your Facebook App Secret",
        required: true,
      },
      {
        id: "access_token",
        label: "Access Token",
        type: "password",
        placeholder: "Enter your long-lived access token",
        required: true,
      },
    ],
    description: "Connect to Facebook to access Page insights, post metrics, and ad performance data.",
    docsUrl: "https://developers.facebook.com/docs/marketing-api/getting-started",
  },
  {
    id: "instagram",
    name: "Instagram",
    icon: Instagram,
    status: "connected",
    lastSynced: "2 hours ago",
    fields: [
      {
        id: "access_token",
        label: "Access Token",
        type: "password",
        placeholder: "Enter your Instagram access token",
        required: true,
      },
      {
        id: "user_id",
        label: "User ID",
        type: "text",
        placeholder: "Enter your Instagram user ID",
        required: true,
      },
    ],
    description: "Connect to Instagram to access post insights, story metrics, and audience data.",
    docsUrl: "https://developers.facebook.com/docs/instagram-api/getting-started",
  },
  {
    id: "twitter",
    name: "Twitter",
    icon: Twitter,
    status: "error",
    lastSynced: "Failed 1 day ago",
    fields: [
      {
        id: "api_key",
        label: "API Key",
        type: "text",
        placeholder: "Enter your Twitter API key",
        required: true,
      },
      {
        id: "api_secret",
        label: "API Secret",
        type: "password",
        placeholder: "Enter your Twitter API secret",
        required: true,
      },
      {
        id: "access_token",
        label: "Access Token",
        type: "password",
        placeholder: "Enter your Twitter access token",
        required: true,
      },
      {
        id: "access_token_secret",
        label: "Access Token Secret",
        type: "password",
        placeholder: "Enter your Twitter access token secret",
        required: true,
      },
    ],
    description: "Connect to Twitter to access tweet metrics, follower growth, and engagement data.",
    docsUrl: "https://developer.twitter.com/en/docs/twitter-api",
  },
  {
    id: "linkedin",
    name: "LinkedIn",
    icon: Linkedin,
    status: "disconnected",
    fields: [
      {
        id: "client_id",
        label: "Client ID",
        type: "text",
        placeholder: "Enter your LinkedIn Client ID",
        required: true,
      },
      {
        id: "client_secret",
        label: "Client Secret",
        type: "password",
        placeholder: "Enter your LinkedIn Client Secret",
        required: true,
      },
      {
        id: "access_token",
        label: "Access Token",
        type: "password",
        placeholder: "Enter your LinkedIn access token",
        required: true,
      },
    ],
    description: "Connect to LinkedIn to access company page analytics, post performance, and follower demographics.",
    docsUrl: "https://learn.microsoft.com/en-us/linkedin/marketing/overview",
  },
  {
    id: "youtube",
    name: "YouTube",
    icon: Youtube,
    status: "disconnected",
    fields: [
      {
        id: "api_key",
        label: "API Key",
        type: "text",
        placeholder: "Enter your YouTube API key",
        required: true,
      },
      {
        id: "client_id",
        label: "Client ID",
        type: "text",
        placeholder: "Enter your YouTube Client ID",
        required: true,
      },
      {
        id: "client_secret",
        label: "Client Secret",
        type: "password",
        placeholder: "Enter your YouTube Client Secret",
        required: true,
      },
      {
        id: "refresh_token",
        label: "Refresh Token",
        type: "password",
        placeholder: "Enter your YouTube refresh token",
        required: false,
      },
    ],
    description: "Connect to YouTube to access video performance, channel growth, and audience engagement metrics.",
    docsUrl: "https://developers.google.com/youtube/v3/getting-started",
  },
]

// Ad platforms
const adPlatforms: PlatformConfig[] = [
  {
    id: "meta_ads",
    name: "Meta Ads",
    icon: Facebook,
    status: "connected",
    lastSynced: "1 hour ago",
    fields: [
      {
        id: "access_token",
        label: "Access Token",
        type: "password",
        placeholder: "Enter your Meta Ads access token",
        required: true,
      },
      {
        id: "ad_account_id",
        label: "Ad Account ID",
        type: "text",
        placeholder: "Enter your Ad Account ID",
        required: true,
      },
    ],
    description: "Connect to Meta Ads to access campaign performance, ad spend, and conversion metrics.",
    docsUrl: "https://developers.facebook.com/docs/marketing-api",
  },
  {
    id: "google_ads",
    name: "Google Ads",
    icon: Search,
    status: "disconnected",
    fields: [
      {
        id: "client_id",
        label: "Client ID",
        type: "text",
        placeholder: "Enter your Google Ads Client ID",
        required: true,
      },
      {
        id: "client_secret",
        label: "Client Secret",
        type: "password",
        placeholder: "Enter your Google Ads Client Secret",
        required: true,
      },
      {
        id: "developer_token",
        label: "Developer Token",
        type: "password",
        placeholder: "Enter your Google Ads Developer Token",
        required: true,
      },
      {
        id: "refresh_token",
        label: "Refresh Token",
        type: "password",
        placeholder: "Enter your Google Ads refresh token",
        required: true,
      },
      {
        id: "customer_id",
        label: "Customer ID",
        type: "text",
        placeholder: "Enter your Google Ads Customer ID",
        required: true,
      },
    ],
    description: "Connect to Google Ads to access campaign performance, ad spend, and conversion metrics.",
    docsUrl: "https://developers.google.com/google-ads/api/docs/start",
  },
  {
    id: "linkedin_ads",
    name: "LinkedIn Ads",
    icon: Linkedin,
    status: "disconnected",
    fields: [
      {
        id: "client_id",
        label: "Client ID",
        type: "text",
        placeholder: "Enter your LinkedIn Ads Client ID",
        required: true,
      },
      {
        id: "client_secret",
        label: "Client Secret",
        type: "password",
        placeholder: "Enter your LinkedIn Ads Client Secret",
        required: true,
      },
      {
        id: "access_token",
        label: "Access Token",
        type: "password",
        placeholder: "Enter your LinkedIn Ads access token",
        required: true,
      },
      {
        id: "account_id",
        label: "Account ID",
        type: "text",
        placeholder: "Enter your LinkedIn Ads Account ID",
        required: true,
      },
    ],
    description: "Connect to LinkedIn Ads to access campaign performance, ad spend, and conversion metrics.",
    docsUrl: "https://learn.microsoft.com/en-us/linkedin/marketing/overview",
  },
]

export function ApiConnectionsSettings() {
  const [searchQuery, setSearchQuery] = useState("")
  const [autoSync, setAutoSync] = useState(true)
  const [selectedPlatform, setSelectedPlatform] = useState<PlatformConfig | null>(null)
  const [isConfiguring, setIsConfiguring] = useState(false)

  // Filter platforms based on search query
  const filteredPlatforms = platforms.filter((platform) =>
    platform.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const filteredAdPlatforms = adPlatforms.filter((platform) =>
    platform.name.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Handle platform connection
  const handleConnect = (platform: PlatformConfig) => {
    setSelectedPlatform(platform)
    setIsConfiguring(true)
  }

  // Handle platform disconnection
  const handleDisconnect = (platformId: string) => {
    toast({
      title: "Platform disconnected",
      description: `You've successfully disconnected from ${platformId}.`,
    })
  }

  // Handle connection test
  const handleTestConnection = (platformId: string) => {
    toast({
      title: "Testing connection",
      description: `Testing connection to ${platformId}...`,
    })
  }

  // Handle form submission
  const handleFormSubmit = (platformId: string, formData: Record<string, string>) => {
    // Here you would typically send this data to your backend
    console.log(`Connecting to ${platformId}`, formData)

    toast({
      title: "Connection successful",
      description: `You've successfully connected to ${platformId}.`,
    })

    setIsConfiguring(false)
  }

  // Render connection status badge
  const renderStatusBadge = (status: ConnectionStatus) => {
    switch (status) {
      case "connected":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300">
            <CheckCircle2 className="mr-1 h-3 w-3" /> Connected
          </Badge>
        )
      case "disconnected":
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300">
            Disconnected
          </Badge>
        )
      case "error":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300">
            <AlertCircle className="mr-1 h-3 w-3" /> Error
          </Badge>
        )
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300">
            <RefreshCw className="mr-1 h-3 w-3 animate-spin" /> Connecting
          </Badge>
        )
      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {isConfiguring && selectedPlatform ? (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <Button variant="ghost" onClick={() => setIsConfiguring(false)}>
              ← Back to connections
            </Button>
          </div>

          <PlatformConnectionForm
            platform={selectedPlatform}
            onSubmit={handleFormSubmit}
            onCancel={() => setIsConfiguring(false)}
          />
        </div>
      ) : (
        <>
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search platforms..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="auto-sync" checked={autoSync} onCheckedChange={setAutoSync} />
              <Label htmlFor="auto-sync">Auto-sync data (every 6 hours)</Label>
            </div>
          </div>

          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>API Connection Security</AlertTitle>
            <AlertDescription>
              Your API keys and secrets are encrypted and stored securely. We never share your credentials with third
              parties.
            </AlertDescription>
          </Alert>

          <Tabs defaultValue="social" className="space-y-4">
            <TabsList>
              <TabsTrigger value="social">Social Media Platforms</TabsTrigger>
              <TabsTrigger value="ads">Ad Platforms</TabsTrigger>
            </TabsList>

            <TabsContent value="social" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredPlatforms.map((platform) => (
                  <Card key={platform.id}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <div className="flex items-center space-x-2">
                        <platform.icon className="h-5 w-5" />
                        <CardTitle className="text-lg">{platform.name}</CardTitle>
                      </div>
                      {renderStatusBadge(platform.status)}
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="min-h-[60px]">{platform.description}</CardDescription>
                      {platform.status === "connected" && platform.lastSynced && (
                        <p className="mt-2 text-xs text-muted-foreground">Last synced: {platform.lastSynced}</p>
                      )}
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      {platform.status === "connected" ? (
                        <>
                          <Button variant="outline" size="sm" onClick={() => handleTestConnection(platform.id)}>
                            Test Connection
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDisconnect(platform.id)}>
                            Disconnect
                          </Button>
                        </>
                      ) : (
                        <Button className="w-full" onClick={() => handleConnect(platform)}>
                          Connect
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="ads" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filteredAdPlatforms.map((platform) => (
                  <Card key={platform.id}>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <div className="flex items-center space-x-2">
                        <platform.icon className="h-5 w-5" />
                        <CardTitle className="text-lg">{platform.name}</CardTitle>
                      </div>
                      {renderStatusBadge(platform.status)}
                    </CardHeader>
                    <CardContent>
                      <CardDescription className="min-h-[60px]">{platform.description}</CardDescription>
                      {platform.status === "connected" && platform.lastSynced && (
                        <p className="mt-2 text-xs text-muted-foreground">Last synced: {platform.lastSynced}</p>
                      )}
                    </CardContent>
                    <CardFooter className="flex justify-between">
                      {platform.status === "connected" ? (
                        <>
                          <Button variant="outline" size="sm" onClick={() => handleTestConnection(platform.id)}>
                            Test Connection
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDisconnect(platform.id)}>
                            Disconnect
                          </Button>
                        </>
                      ) : (
                        <Button className="w-full" onClick={() => handleConnect(platform)}>
                          Connect
                        </Button>
                      )}
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </>
      )}
    </div>
  )
}

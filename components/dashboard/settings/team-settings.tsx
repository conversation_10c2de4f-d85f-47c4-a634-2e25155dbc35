"use client"

import { <PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { toast } from "@/components/ui/use-toast"

const teamMembers = [
  {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Admin",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 2,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Editor",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 3,
    name: "<PERSON>",
    email: "<EMAIL>",
    role: "Viewer",
    avatar: "/placeholder.svg?height=40&width=40",
  },
  {
    id: 4,
    name: "<PERSON>",
    email: "micha<PERSON>.<EMAIL>",
    role: "Editor",
    avatar: "/placeholder.svg?height=40&width=40",
  },
]

export function TeamSettings() {
  const handleInvite = () => {
    toast({
      title: "Invitation sent",
      description: "Your team member has been invited to join the dashboard.",
    })
  }

  const handleRemoveMember = (id: number) => {
    toast({
      title: "Team member removed",
      description: "The team member has been removed from your account.",
    })
  }

  const handleRoleChange = (id: number, role: string) => {
    toast({
      title: "Role updated",
      description: `Team member's role has been updated to ${role}.`,
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Team Management</CardTitle>
        <CardDescription>Manage your team members and their access permissions</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">Team Members</h3>
            <Button size="sm">
              <PlusCircle className="mr-2 h-4 w-4" />
              Add Member
            </Button>
          </div>

          <div className="space-y-4">
            {teamMembers.map((member) => (
              <div key={member.id} className="flex items-center justify-between rounded-lg border p-3">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={member.avatar || "/placeholder.svg"} alt={member.name} />
                    <AvatarFallback>{member.name.substring(0, 2)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{member.name}</p>
                    <p className="text-sm text-muted-foreground">{member.email}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Select defaultValue={member.role} onValueChange={(value) => handleRoleChange(member.id, value)}>
                    <SelectTrigger className="w-[110px]">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Admin">Admin</SelectItem>
                      <SelectItem value="Editor">Editor</SelectItem>
                      <SelectItem value="Viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="ghost" size="icon" onClick={() => handleRemoveMember(member.id)}>
                    <Trash2 className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Invite New Member</h3>
          <div className="flex space-x-2">
            <Input placeholder="Email address" className="flex-1" />
            <Select defaultValue="Editor">
              <SelectTrigger className="w-[110px]">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Admin">Admin</SelectItem>
                <SelectItem value="Editor">Editor</SelectItem>
                <SelectItem value="Viewer">Viewer</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleInvite}>Invite</Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex flex-col items-start space-y-2">
        <h4 className="text-sm font-medium">Role Permissions</h4>
        <ul className="text-sm text-muted-foreground">
          <li>
            • <span className="font-medium">Admin:</span> Full access to all features and settings
          </li>
          <li>
            • <span className="font-medium">Editor:</span> Can view and edit content, but cannot manage team or billing
          </li>
          <li>
            • <span className="font-medium">Viewer:</span> Read-only access to dashboards and reports
          </li>
        </ul>
      </CardFooter>
    </Card>
  )
}

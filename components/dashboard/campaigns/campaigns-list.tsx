"use client"

import { useState, useMemo, memo, useCallback } from "react"
import { MoreHorizontal, Search } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const campaignsData = [
  {
    id: 1,
    name: "Summer Sale 2023",
    status: "Active",
    objective: "Sales",
    platforms: ["Facebook", "Instagram"],
    budget: "$5,000",
    spent: "$2,345",
    startDate: "2023-06-01",
    endDate: "2023-06-30",
    clicks: 12500,
    conversions: 380,
    roas: 3.2,
  },
  {
    id: 2,
    name: "Product Launch",
    status: "Active",
    objective: "Awareness",
    platforms: ["Instagram", "Twitter"],
    budget: "$8,000",
    spent: "$4,120",
    startDate: "2023-05-15",
    endDate: "2023-06-15",
    clicks: 18900,
    conversions: 420,
    roas: 2.8,
  },
  {
    id: 3,
    name: "Lead Generation Q2",
    status: "Scheduled",
    objective: "Leads",
    platforms: ["LinkedIn", "Facebook"],
    budget: "$6,500",
    spent: "$0",
    startDate: "2023-07-01",
    endDate: "2023-07-31",
    clicks: 0,
    conversions: 0,
    roas: 0,
  },
  {
    id: 4,
    name: "Brand Awareness",
    status: "Completed",
    objective: "Awareness",
    platforms: ["Twitter", "Instagram"],
    budget: "$3,500",
    spent: "$3,500",
    startDate: "2023-04-01",
    endDate: "2023-04-30",
    clicks: 9800,
    conversions: 210,
    roas: 2.4,
  },
  {
    id: 5,
    name: "Holiday Special",
    status: "Draft",
    objective: "Sales",
    platforms: ["Facebook", "Instagram", "LinkedIn"],
    budget: "$10,000",
    spent: "$0",
    startDate: "2023-12-01",
    endDate: "2023-12-31",
    clicks: 0,
    conversions: 0,
    roas: 0,
  },
]

// Utility function to get status color classes
const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case "active":
      return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
    case "scheduled":
      return "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
    case "completed":
      return "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-100"
    case "draft":
      return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
    default:
      return ""
  }
}

export const CampaignsList = memo(function CampaignsList() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")

  // Memoized filtered campaigns for better performance
  const filteredCampaigns = useMemo(() => {
    return campaignsData.filter((campaign) => {
      const matchesSearch = campaign.name.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === "all" || campaign.status.toLowerCase() === statusFilter.toLowerCase()
      return matchesSearch && matchesStatus
    })
  }, [searchQuery, statusFilter])

  // Memoized callbacks to prevent unnecessary re-renders
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value)
  }, [])

  const handleStatusChange = useCallback((value: string) => {
    setStatusFilter(value)
  }, [])

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div>
            <CardTitle>Campaigns</CardTitle>
            <CardDescription>Manage and track your marketing campaigns</CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search campaigns..."
                className="pl-8"
                value={searchQuery}
                onChange={handleSearchChange}
              />
            </div>
            <Select value={statusFilter} onValueChange={handleStatusChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Campaign</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Platforms</TableHead>
                  <TableHead>Budget</TableHead>
                  <TableHead>Spent</TableHead>
                  <TableHead className="text-right">ROAS</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCampaigns.map((campaign) => (
                  <TableRow key={campaign.id}>
                    <TableCell>
                      <div className="font-medium">{campaign.name}</div>
                      <div className="text-sm text-muted-foreground">{campaign.objective}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getStatusColor(campaign.status)}>
                        {campaign.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-wrap gap-1">
                        {campaign.platforms.map((platform) => (
                          <Badge key={platform} variant="secondary">
                            {platform}
                          </Badge>
                        ))}
                      </div>
                    </TableCell>
                    <TableCell>{campaign.budget}</TableCell>
                    <TableCell>{campaign.spent}</TableCell>
                    <TableCell className="text-right">{campaign.roas > 0 ? `${campaign.roas}x` : "-"}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem>View details</DropdownMenuItem>
                          <DropdownMenuItem>Edit campaign</DropdownMenuItem>
                          <DropdownMenuItem>Duplicate</DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-red-600">Delete campaign</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  )
})

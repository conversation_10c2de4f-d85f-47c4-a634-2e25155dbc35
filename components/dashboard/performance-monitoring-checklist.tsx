"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { CheckCircle, AlertCircle, Clock, TrendingUp } from 'lucide-react'

interface ChecklistItem {
  id: string
  title: string
  description: string
  category: 'performance' | 'functionality' | 'monitoring' | 'optimization'
  priority: 'high' | 'medium' | 'low'
  completed: boolean
}

const initialChecklist: ChecklistItem[] = [
  {
    id: 'dashboard-loads',
    title: 'Dashboard Loads Successfully',
    description: 'Performance dashboard loads at /dashboard/performance without errors',
    category: 'functionality',
    priority: 'high',
    completed: true
  },
  {
    id: 'charts-render',
    title: 'Charts Render Correctly',
    description: 'All chart components display data without undefined component errors',
    category: 'functionality',
    priority: 'high',
    completed: true
  },
  {
    id: 'error-boundaries',
    title: 'Error Boundaries Active',
    description: 'Error boundaries catch and display fallback UI for component failures',
    category: 'functionality',
    priority: 'high',
    completed: true
  },
  {
    id: 'lazy-loading',
    title: 'Lazy Loading Implemented',
    description: 'Components load on-demand to improve initial page load time',
    category: 'performance',
    priority: 'medium',
    completed: true
  },
  {
    id: 'memoization',
    title: 'Component Memoization',
    description: 'React.memo() applied to prevent unnecessary re-renders',
    category: 'performance',
    priority: 'medium',
    completed: true
  },
  {
    id: 'bundle-size',
    title: 'Bundle Size Optimized',
    description: 'Each component is under 8KB and properly code-split',
    category: 'optimization',
    priority: 'medium',
    completed: true
  },
  {
    id: 'memory-monitoring',
    title: 'Memory Usage Monitoring',
    description: 'Track memory consumption and prevent memory leaks',
    category: 'monitoring',
    priority: 'medium',
    completed: false
  },
  {
    id: 'performance-metrics',
    title: 'Performance Metrics Tracking',
    description: 'Monitor load times, render times, and user interactions',
    category: 'monitoring',
    priority: 'low',
    completed: false
  },
  {
    id: 'error-tracking',
    title: 'Error Tracking Setup',
    description: 'Implement error tracking for production monitoring',
    category: 'monitoring',
    priority: 'medium',
    completed: false
  },
  {
    id: 'accessibility',
    title: 'Accessibility Compliance',
    description: 'Ensure dashboard meets WCAG 2.1 accessibility standards',
    category: 'optimization',
    priority: 'low',
    completed: false
  }
]

export default function PerformanceMonitoringChecklist() {
  const [checklist, setChecklist] = useState<ChecklistItem[]>(initialChecklist)
  const [filter, setFilter] = useState<string>('all')

  const toggleItem = (id: string) => {
    setChecklist(prev => 
      prev.map(item => 
        item.id === id ? { ...item, completed: !item.completed } : item
      )
    )
  }

  const filteredItems = checklist.filter(item => 
    filter === 'all' || item.category === filter
  )

  const completedCount = checklist.filter(item => item.completed).length
  const totalCount = checklist.length
  const completionPercentage = Math.round((completedCount / totalCount) * 100)

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'performance': return <TrendingUp className="h-4 w-4" />
      case 'functionality': return <CheckCircle className="h-4 w-4" />
      case 'monitoring': return <Clock className="h-4 w-4" />
      case 'optimization': return <AlertCircle className="h-4 w-4" />
      default: return <CheckCircle className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive'
      case 'medium': return 'default'
      case 'low': return 'secondary'
      default: return 'default'
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle className="h-5 w-5" />
          Performance Dashboard Monitoring Checklist
        </CardTitle>
        <CardDescription>
          Track the health and performance of the dashboard components
        </CardDescription>
        <div className="flex items-center gap-4 mt-4">
          <div className="text-2xl font-bold text-green-600">
            {completionPercentage}%
          </div>
          <div className="text-sm text-muted-foreground">
            {completedCount} of {totalCount} items completed
          </div>
          <div className="flex-1 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${completionPercentage}%` }}
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 mb-6 flex-wrap">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            All Items
          </Button>
          <Button
            variant={filter === 'functionality' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('functionality')}
          >
            Functionality
          </Button>
          <Button
            variant={filter === 'performance' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('performance')}
          >
            Performance
          </Button>
          <Button
            variant={filter === 'monitoring' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('monitoring')}
          >
            Monitoring
          </Button>
          <Button
            variant={filter === 'optimization' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('optimization')}
          >
            Optimization
          </Button>
        </div>

        <div className="space-y-4">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className={`flex items-start gap-3 p-4 rounded-lg border transition-all duration-200 ${
                item.completed 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-white border-gray-200 hover:border-gray-300'
              }`}
            >
              <Checkbox
                id={item.id}
                checked={item.completed}
                onCheckedChange={() => toggleItem(item.id)}
                className="mt-1"
              />
              <div className="flex-1 space-y-2">
                <div className="flex items-center gap-2">
                  {getCategoryIcon(item.category)}
                  <label
                    htmlFor={item.id}
                    className={`font-medium cursor-pointer ${
                      item.completed ? 'line-through text-gray-500' : ''
                    }`}
                  >
                    {item.title}
                  </label>
                  <Badge variant={getPriorityColor(item.priority) as any}>
                    {item.priority}
                  </Badge>
                </div>
                <p className={`text-sm ${
                  item.completed ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {item.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No items found for the selected filter.
          </div>
        )}
      </CardContent>
    </Card>
  )
}

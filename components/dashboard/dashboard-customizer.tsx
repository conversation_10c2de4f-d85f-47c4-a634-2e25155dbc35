"use client"

import type React from "react"

import { useState, useEffect, Suspense, lazy } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Grip, Save, Undo, Lock, Unlock } from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// Lazy load drag and drop functionality
const LazyDragDropProvider = lazy(() =>
  import("@dnd-kit/core").then(module => ({
    default: ({ children, onDragEnd }: any) => {
      const { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } = module

      const sensors = useSensors(
        useSensor(PointerSensor, {
          activationConstraint: { distance: 8 },
        }),
        useSensor(KeyboardSensor, {
          coordinateGetter: (event: any, { context }: any) => {
            // Simple keyboard coordinate getter
            return { x: 0, y: 0 }
          },
        }),
      )

      return (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={onDragEnd}
        >
          {children}
        </DndContext>
      )
    }
  }))
)

const LazySortableContext = lazy(() =>
  import("@dnd-kit/sortable").then(module => ({
    default: module.SortableContext
  }))
)

const LazySortableWidget = lazy(() =>
  import("./components/SortableWidget").then(module => ({
    default: module.SortableWidget
  }))
)

const DragDropSkeleton = () => (
  <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
    <div className="col-span-full h-32 bg-muted animate-pulse rounded flex items-center justify-center">
      <div className="text-muted-foreground">Loading drag & drop...</div>
    </div>
  </div>
)

// Widget interface
export interface DashboardWidget {
  id: string
  title: string
  description?: string
  size: "small" | "medium" | "large" | "full"
  component: React.ComponentType<any>
  locked?: boolean
}

// Props for the DashboardCustomizer component
interface DashboardCustomizerProps {
  widgets: DashboardWidget[]
  onSave?: (widgets: DashboardWidget[]) => void
  defaultLayout?: string[]
}

// Static widget component for non-customization mode
function StaticWidget({ widget }: { widget: DashboardWidget }) {
  const getGridClass = () => {
    switch (widget.size) {
      case "small": return "col-span-1"
      case "medium": return "col-span-1 md:col-span-2"
      case "large": return "col-span-1 md:col-span-2 lg:col-span-3"
      case "full": return "col-span-full"
      default: return "col-span-1"
    }
  }

  return (
    <div className={getGridClass()}>
      <Card className="h-full">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">{widget.title}</CardTitle>
          {widget.description && <CardDescription>{widget.description}</CardDescription>}
        </CardHeader>
        <CardContent>
          <widget.component />
        </CardContent>
      </Card>
    </div>
  )
}

// Main DashboardCustomizer component
export function DashboardCustomizer({ widgets, onSave, defaultLayout }: DashboardCustomizerProps) {
  const [items, setItems] = useState<DashboardWidget[]>(widgets)
  const [isCustomizing, setIsCustomizing] = useState(false)
  const [originalLayout, setOriginalLayout] = useState<DashboardWidget[]>(widgets)

  // Load saved layout from localStorage on component mount
  useEffect(() => {
    const savedLayout = localStorage.getItem("dashboardLayout")
    if (savedLayout) {
      try {
        const savedIds = JSON.parse(savedLayout) as string[]

        // Reorder widgets based on saved layout
        const reorderedWidgets = [...widgets]
        const orderedWidgets: DashboardWidget[] = []

        // First add widgets in the saved order
        savedIds.forEach((id) => {
          const widget = reorderedWidgets.find((w) => w.id === id)
          if (widget) {
            orderedWidgets.push(widget)
            reorderedWidgets.splice(
              reorderedWidgets.findIndex((w) => w.id === id),
              1,
            )
          }
        })

        // Then add any new widgets that weren't in the saved layout
        setItems([...orderedWidgets, ...reorderedWidgets])
      } catch (error) {
        console.error("Error loading dashboard layout:", error)
      }
    }
  }, [widgets])

  // Handle drag end event
  const handleDragEnd = (event: any) => {
    const { active, over } = event

    if (active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)

        // Simple array move implementation
        const newItems = [...items]
        const [movedItem] = newItems.splice(oldIndex, 1)
        newItems.splice(newIndex, 0, movedItem)
        return newItems
      })
    }
  }

  // Toggle customization mode
  const toggleCustomizing = () => {
    if (!isCustomizing) {
      setOriginalLayout([...items])
    }
    setIsCustomizing(!isCustomizing)
  }

  // Save the current layout
  const saveLayout = () => {
    const layoutIds = items.map((item) => item.id)
    localStorage.setItem("dashboardLayout", JSON.stringify(layoutIds))

    if (onSave) {
      onSave(items)
    }

    setIsCustomizing(false)
    toast({
      title: "Dashboard layout saved",
      description: "Your custom dashboard layout has been saved.",
    })
  }

  // Reset to original layout
  const resetLayout = () => {
    setItems(originalLayout)
  }

  // Filter out locked widgets that can't be dragged
  const draggableItems = items.filter((widget) => !widget.locked)

  return (
    <div className="space-y-4">
      {/* Customization controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant={isCustomizing ? "default" : "outline"}
            size="sm"
            onClick={toggleCustomizing}
            className="gap-1"
          >
            {isCustomizing ? <Lock className="h-4 w-4" /> : <Unlock className="h-4 w-4" />}
            {isCustomizing ? "Done" : "Customize Dashboard"}
          </Button>

          {isCustomizing && (
            <>
              <Button variant="outline" size="sm" onClick={resetLayout} className="gap-1">
                <Undo className="h-4 w-4" />
                Reset
              </Button>
              <Button variant="default" size="sm" onClick={saveLayout} className="gap-1">
                <Save className="h-4 w-4" />
                Save Layout
              </Button>
            </>
          )}
        </div>

        {isCustomizing && <p className="text-sm text-muted-foreground">Drag widgets to rearrange your dashboard</p>}
      </div>

      {/* Dashboard grid with draggable widgets */}
      {isCustomizing ? (
        <Suspense fallback={<DragDropSkeleton />}>
          <LazyDragDropProvider onDragEnd={handleDragEnd}>
            <LazySortableContext items={draggableItems.map((item) => item.id)}>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                {items.map((widget) => (
                  <Suspense key={widget.id} fallback={<div className="h-32 bg-muted animate-pulse rounded" />}>
                    <LazySortableWidget widget={widget} />
                  </Suspense>
                ))}
              </div>
            </LazySortableContext>
          </LazyDragDropProvider>
        </Suspense>
      ) : (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {items.map((widget) => (
            <StaticWidget key={widget.id} widget={widget} />
          ))}
        </div>
      )}
    </div>
  )
}

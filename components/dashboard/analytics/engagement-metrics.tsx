"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { TrendingUp, TrendingDown, Heart, MessageSquare, Share2 } from "lucide-react"

const engagementData = [
  {
    name: "Jan",
    likes: 4000,
    comments: 2400,
    shares: 1200,
  },
  {
    name: "Feb",
    likes: 3000,
    comments: 1398,
    shares: 900,
  },
  {
    name: "Mar",
    likes: 2000,
    comments: 9800,
    shares: 1800,
  },
  {
    name: "Apr",
    likes: 2780,
    comments: 3908,
    shares: 2500,
  },
  {
    name: "May",
    likes: 1890,
    comments: 4800,
    shares: 2300,
  },
  {
    name: "Jun",
    likes: 2390,
    comments: 3800,
    shares: 2100,
  },
  {
    name: "Jul",
    likes: 3490,
    comments: 4300,
    shares: 2400,
  },
]

export function EngagementMetrics() {
  const [activeTab, setActiveTab] = useState("overview")

  // Calculate totals and trends for simplified display
  const totalLikes = engagementData.reduce((sum, item) => sum + item.likes, 0)
  const totalComments = engagementData.reduce((sum, item) => sum + item.comments, 0)
  const totalShares = engagementData.reduce((sum, item) => sum + item.shares, 0)

  const avgLikes = Math.round(totalLikes / engagementData.length)
  const avgComments = Math.round(totalComments / engagementData.length)
  const avgShares = Math.round(totalShares / engagementData.length)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Engagement Metrics</CardTitle>
        <CardDescription>Track likes, comments, and shares across platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="likes">Likes</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
            <TabsTrigger value="shares">Shares</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Heart className="h-4 w-4 text-red-500" />
                    <span className="text-sm font-medium">Likes</span>
                  </div>
                  <TrendingUp className="h-4 w-4 text-green-500" />
                </div>
                <div className="text-2xl font-bold">{totalLikes.toLocaleString()}</div>
                <Progress value={75} className="h-2" />
                <p className="text-xs text-muted-foreground">Avg: {avgLikes.toLocaleString()}/month</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Comments</span>
                  </div>
                  <TrendingUp className="h-4 w-4 text-green-500" />
                </div>
                <div className="text-2xl font-bold">{totalComments.toLocaleString()}</div>
                <Progress value={60} className="h-2" />
                <p className="text-xs text-muted-foreground">Avg: {avgComments.toLocaleString()}/month</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Share2 className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Shares</span>
                  </div>
                  <TrendingDown className="h-4 w-4 text-red-500" />
                </div>
                <div className="text-2xl font-bold">{totalShares.toLocaleString()}</div>
                <Progress value={45} className="h-2" />
                <p className="text-xs text-muted-foreground">Avg: {avgShares.toLocaleString()}/month</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="likes">
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-red-500">{totalLikes.toLocaleString()}</div>
                <p className="text-sm text-muted-foreground">Total Likes</p>
              </div>
              <div className="space-y-2">
                {engagementData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{item.name}</span>
                    <div className="flex items-center gap-2">
                      <Progress value={(item.likes / Math.max(...engagementData.map(d => d.likes))) * 100} className="w-20 h-2" />
                      <span className="text-sm font-medium w-16 text-right">{item.likes.toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="comments">
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-500">{totalComments.toLocaleString()}</div>
                <p className="text-sm text-muted-foreground">Total Comments</p>
              </div>
              <div className="space-y-2">
                {engagementData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{item.name}</span>
                    <div className="flex items-center gap-2">
                      <Progress value={(item.comments / Math.max(...engagementData.map(d => d.comments))) * 100} className="w-20 h-2" />
                      <span className="text-sm font-medium w-16 text-right">{item.comments.toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="shares">
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-500">{totalShares.toLocaleString()}</div>
                <p className="text-sm text-muted-foreground">Total Shares</p>
              </div>
              <div className="space-y-2">
                {engagementData.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm">{item.name}</span>
                    <div className="flex items-center gap-2">
                      <Progress value={(item.shares / Math.max(...engagementData.map(d => d.shares))) * 100} className="w-20 h-2" />
                      <span className="text-sm font-medium w-16 text-right">{item.shares.toLocaleString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

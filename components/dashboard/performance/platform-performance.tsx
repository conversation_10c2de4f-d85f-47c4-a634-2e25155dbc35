"use client"

import { memo } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { ArrowUp, ArrowDown } from "lucide-react"

export const PlatformPerformance = memo(function PlatformPerformance() {
  const platforms = [
    {
      name: "Instagram",
      reach: 890000,
      engagement: 5.2,
      growth: 12.3,
      trend: "up",
      score: 92,
      color: "bg-gradient-to-r from-purple-500 to-pink-500",
    },
    {
      name: "LinkedIn",
      reach: 456000,
      engagement: 4.8,
      growth: 8.7,
      trend: "up",
      score: 88,
      color: "bg-blue-600",
    },
    {
      name: "Facebook",
      reach: 678000,
      engagement: 3.9,
      growth: -2.1,
      trend: "down",
      score: 75,
      color: "bg-blue-500",
    },
    {
      name: "Twitter",
      reach: 234000,
      engagement: 3.2,
      growth: 5.4,
      trend: "up",
      score: 72,
      color: "bg-sky-500",
    },
    {
      name: "TikTok",
      reach: 567000,
      engagement: 6.8,
      growth: 28.9,
      trend: "up",
      score: 85,
      color: "bg-black",
    },
  ]

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-emerald-600"
    if (score >= 80) return "text-blue-600"
    if (score >= 70) return "text-orange-600"
    return "text-red-600"
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Performance</CardTitle>
        <CardDescription>Compare performance across social media platforms</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {platforms.map((platform) => (
            <div key={platform.name} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`h-3 w-3 rounded-full ${platform.color}`} />
                  <span className="font-medium">{platform.name}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className={`text-sm font-medium ${getScoreColor(platform.score)}`}>{platform.score}/100</span>
                  <Badge variant="outline">
                    {platform.trend === "up" ? (
                      <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
                    ) : (
                      <ArrowDown className="mr-1 h-3 w-3 text-red-500" />
                    )}
                    {Math.abs(platform.growth)}%
                  </Badge>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground">Reach</div>
                  <div className="font-medium">{platform.reach.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Engagement</div>
                  <div className="font-medium">{platform.engagement}%</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Growth</div>
                  <div className={`font-medium ${platform.trend === "up" ? "text-emerald-600" : "text-red-600"}`}>
                    {platform.growth > 0 ? "+" : ""}
                    {platform.growth}%
                  </div>
                </div>
              </div>
              <Progress value={platform.score} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
})

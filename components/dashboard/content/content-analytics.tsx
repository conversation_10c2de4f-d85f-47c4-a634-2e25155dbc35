"use client"

import { <PERSON><PERSON><PERSON>, ArrowUp, Eye, Heart, MessageSquare, Share2 } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function ContentAnalytics() {
  const metrics = [
    {
      title: "Total Posts",
      value: "124",
      change: "+8",
      trend: "up",
      icon: Eye,
    },
    {
      title: "Total Likes",
      value: "24,892",
      change: "+12.3%",
      trend: "up",
      icon: Heart,
    },
    {
      title: "Total Comments",
      value: "3,845",
      change: "-2.5%",
      trend: "down",
      icon: MessageSquare,
    },
    {
      title: "Total Shares",
      value: "5,267",
      change: "+15.2%",
      trend: "up",
      icon: Share2,
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="flex items-center text-xs">
              {metric.trend === "up" ? (
                <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-rose-500" />
              )}
              <span className={metric.trend === "up" ? "text-emerald-500" : "text-rose-500"}>{metric.change}</span>
              <span className="ml-1 text-muted-foreground">vs last period</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

"use client"

import { ArrowDown, ArrowUp, Users, UserPlus, UserMinus, Target, Globe, Heart } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export function AudienceOverview() {
  const metrics = [
    {
      title: "Total Followers",
      value: "124,892",
      change: "+12.3%",
      trend: "up",
      icon: Users,
      description: "Across all platforms",
    },
    {
      title: "New Followers",
      value: "+2,847",
      change: "+18.7%",
      trend: "up",
      icon: UserPlus,
      description: "This month",
    },
    {
      title: "Unfollows",
      value: "234",
      change: "-5.2%",
      trend: "down",
      icon: UserMinus,
      description: "This month",
    },
    {
      title: "Engagement Rate",
      value: "4.2%",
      change: "+0.8%",
      trend: "up",
      icon: Heart,
      description: "Average across platforms",
    },
    {
      title: "Reach",
      value: "2.4M",
      change: "+15.4%",
      trend: "up",
      icon: Globe,
      description: "Monthly unique reach",
    },
    {
      title: "Target Audience",
      value: "78%",
      change: "+3.1%",
      trend: "up",
      icon: Target,
      description: "Match with ideal customer",
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {metrics.map((metric) => (
        <Card key={metric.title}>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
            <metric.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metric.value}</div>
            <div className="flex items-center text-xs">
              {metric.trend === "up" ? (
                <ArrowUp className="mr-1 h-3 w-3 text-emerald-500" />
              ) : (
                <ArrowDown className="mr-1 h-3 w-3 text-rose-500" />
              )}
              <span className={metric.trend === "up" ? "text-emerald-500" : "text-rose-500"}>{metric.change}</span>
              <span className="ml-1 text-muted-foreground">vs last period</span>
            </div>
            <p className="mt-2 text-xs text-muted-foreground">{metric.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

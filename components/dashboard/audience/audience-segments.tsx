"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Users, Target, Edit } from "lucide-react"

export function AudienceSegments() {
  const segments = [
    {
      name: "Tech Enthusiasts",
      size: 45230,
      percentage: 36.2,
      engagement: 5.8,
      growth: 12.3,
      description: "Highly engaged users interested in technology and innovation",
      color: "bg-blue-500",
    },
    {
      name: "Business Professionals",
      size: 38750,
      percentage: 31.0,
      engagement: 4.2,
      growth: 8.7,
      description: "Decision makers and business leaders",
      color: "bg-emerald-500",
    },
    {
      name: "Creative Designers",
      size: 24890,
      percentage: 19.9,
      engagement: 6.1,
      growth: 15.2,
      description: "Designers and creative professionals",
      color: "bg-purple-500",
    },
    {
      name: "Marketing Specialists",
      size: 16022,
      percentage: 12.8,
      engagement: 4.9,
      growth: 6.8,
      description: "Marketing professionals and agencies",
      color: "bg-orange-500",
    },
  ]

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Audience Segments</CardTitle>
            <CardDescription>Custom audience segments based on behavior and interests</CardDescription>
          </div>
          <Button variant="outline" size="sm">
            <Target className="mr-2 h-4 w-4" />
            Create Segment
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {segments.map((segment, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`h-3 w-3 rounded-full ${segment.color}`} />
                  <span className="font-medium">{segment.name}</span>
                  <Badge variant="outline">
                    <Users className="mr-1 h-3 w-3" />
                    {segment.size.toLocaleString()}
                  </Badge>
                </div>
                <Button variant="ghost" size="sm">
                  <Edit className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">{segment.description}</p>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <div className="text-muted-foreground">Size</div>
                  <div className="font-medium">{segment.percentage}%</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Engagement</div>
                  <div className="font-medium">{segment.engagement}%</div>
                </div>
                <div>
                  <div className="text-muted-foreground">Growth</div>
                  <div className="font-medium text-emerald-600">+{segment.growth}%</div>
                </div>
              </div>
              <Progress value={segment.percentage} className="h-2" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

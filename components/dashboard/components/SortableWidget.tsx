"use client"

import React from "react"
import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Grip, Lock } from "lucide-react"
import { cn } from "@/lib/utils"
import type { DashboardWidget } from "../dashboard-customizer"

interface SortableWidgetProps {
  widget: DashboardWidget
}

export function SortableWidget({ widget }: SortableWidgetProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: widget.id,
    disabled: widget.locked,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  const getGridClass = () => {
    switch (widget.size) {
      case "small": return "col-span-1"
      case "medium": return "col-span-1 md:col-span-2"
      case "large": return "col-span-1 md:col-span-2 lg:col-span-3"
      case "full": return "col-span-full"
      default: return "col-span-1"
    }
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        getGridClass(),
        isDragging && "z-50 opacity-50",
        widget.locked && "opacity-75"
      )}
    >
      <Card className={cn(
        "h-full relative group",
        !widget.locked && "hover:shadow-md transition-shadow",
        isDragging && "shadow-lg"
      )}>
        {/* Drag handle - only show for non-locked widgets */}
        {!widget.locked && (
          <div className="absolute top-2 right-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-muted-foreground hover:text-foreground cursor-grab active:cursor-grabbing"
              {...attributes}
              {...listeners}
            >
              <Grip className="h-3 w-3" />
              <span className="sr-only">Drag to reorder widget</span>
            </Button>
          </div>
        )}

        {/* Lock indicator for locked widgets */}
        {widget.locked && (
          <div className="absolute top-2 right-2 z-10">
            <div className="h-6 w-6 rounded-full bg-muted flex items-center justify-center">
              <Lock className="h-3 w-3 text-muted-foreground" />
            </div>
          </div>
        )}

        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium pr-8">{widget.title}</CardTitle>
          {widget.description && (
            <CardDescription className="pr-8">{widget.description}</CardDescription>
          )}
        </CardHeader>
        <CardContent>
          <widget.component />
        </CardContent>
      </Card>
    </div>
  )
}

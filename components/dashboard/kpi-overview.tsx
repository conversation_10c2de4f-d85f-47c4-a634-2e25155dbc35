"use client"

import { memo, useMemo } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { ArrowDownIcon, ArrowUpIcon, TrendingUp, Users, DollarSign, MousePointer } from "lucide-react"

export const KPIOverview = memo(function KPIOverview() {
  const metrics = useMemo(() => [
    {
      title: "Total Leads",
      value: "2,853",
      change: "+12.5%",
      trend: "up",
      icon: Users,
    },
    {
      title: "Conversion Rate",
      value: "3.6%",
      change: "-0.8%",
      trend: "down",
      icon: TrendingUp,
    },
    {
      title: "Ad Spend",
      value: "$12,234",
      change: "+8.2%",
      trend: "up",
      icon: DollarSign,
    },
    {
      title: "Click-Through Rate",
      value: "2.1%",
      change: "+0.3%",
      trend: "up",
      icon: MousePointer,
    },
  ], [])

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {metrics.map((metric) => (
        <MetricCard key={metric.title} metric={metric} />
      ))}
    </div>
  )
})

const MetricCard = memo(function MetricCard({ metric }: { metric: any }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{metric.title}</CardTitle>
        <metric.icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{metric.value}</div>
        <p className="text-xs text-muted-foreground">
          <span className={`flex items-center ${metric.trend === "up" ? "text-green-500" : "text-red-500"}`}>
            {metric.trend === "up" ? (
              <ArrowUpIcon className="mr-1 h-4 w-4" />
            ) : (
              <ArrowDownIcon className="mr-1 h-4 w-4" />
            )}
            {metric.change}
          </span>{" "}
          from previous period
        </p>
      </CardContent>
    </Card>
  )
})

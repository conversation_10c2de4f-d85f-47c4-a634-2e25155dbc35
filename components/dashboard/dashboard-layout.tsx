"use client"

import type React from "react"

import { useState } from "react"
import { DashboardSidebar } from "./dashboard-sidebar"
import { SidebarProvider, SidebarInset } from "@/components/ui/sidebar"
import { ModeToggle } from "../mode-toggle"
import { UserNav } from "./user-nav"
import { BreadcrumbNav } from "./breadcrumb-nav"

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(true)

  return (
    <SidebarProvider defaultOpen={sidebarOpen} onOpenChange={setSidebarOpen}>
      <div className="flex min-h-screen">
        <DashboardSidebar />
        <SidebarInset className="flex-1">
          <header className="sticky top-0 z-10 flex h-16 items-center justify-between border-b bg-background px-6">
            <h1 className="text-xl font-semibold">Social Media Marketing Dashboard</h1>
            <div className="flex items-center gap-4">
              <ModeToggle />
              <UserNav />
            </div>
          </header>
          <main className="flex-1 p-6">
            <BreadcrumbNav />
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}

"use client"
import { useEffect, useState } from "react"
import { Activity, TrendingUp, Users, DollarSign, Eye } from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface RealTimeMetrics {
  totalClicks: number
  uniqueClicks: number
  conversions: number
  revenue: number
  clicksToday: number
  conversionRate: number
  revenueToday: number
}

interface LiveMetricsCardsProps {
  metrics: RealTimeMetrics
  isLive: boolean
  previousMetrics?: RealTimeMetrics
}

export function LiveMetricsCards({ metrics, isLive, previousMetrics }: LiveMetricsCardsProps) {
  const [highlightedCards, setHighlightedCards] = useState<Set<string>>(new Set())

  // Track changes and highlight cards
  useEffect(() => {
    if (!previousMetrics) return

    const changes = new Set<string>()

    if (metrics.totalClicks !== previousMetrics.totalClicks) changes.add("clicks")
    if (metrics.conversions !== previousMetrics.conversions) changes.add("conversions")
    if (metrics.revenue !== previousMetrics.revenue) changes.add("revenue")
    if (metrics.clicksToday !== previousMetrics.clicksToday) changes.add("today")

    if (changes.size > 0) {
      setHighlightedCards(changes)
      // Remove highlight after animation
      setTimeout(() => setHighlightedCards(new Set()), 2000)
    }
  }, [metrics, previousMetrics])

  const cards = [
    {
      id: "clicks",
      title: "Total Clicks",
      value: metrics.totalClicks.toLocaleString(),
      icon: Eye,
      change: previousMetrics ? metrics.totalClicks - previousMetrics.totalClicks : 0,
      className: highlightedCards.has("clicks") ? "ring-2 ring-blue-500 ring-opacity-50" : "",
    },
    {
      id: "unique",
      title: "Unique Visitors",
      value: metrics.uniqueClicks.toLocaleString(),
      icon: Users,
      change: previousMetrics ? metrics.uniqueClicks - previousMetrics.uniqueClicks : 0,
      percentage: ((metrics.uniqueClicks / metrics.totalClicks) * 100).toFixed(1),
    },
    {
      id: "conversions",
      title: "Conversions",
      value: metrics.conversions.toString(),
      icon: TrendingUp,
      change: previousMetrics ? metrics.conversions - previousMetrics.conversions : 0,
      percentage: metrics.conversionRate.toFixed(2),
      className: highlightedCards.has("conversions") ? "ring-2 ring-green-500 ring-opacity-50" : "",
    },
    {
      id: "revenue",
      title: "Total Revenue",
      value: `$${metrics.revenue.toFixed(2)}`,
      icon: DollarSign,
      change: previousMetrics ? metrics.revenue - previousMetrics.revenue : 0,
      className: highlightedCards.has("revenue") ? "ring-2 ring-emerald-500 ring-opacity-50" : "",
    },
    {
      id: "today",
      title: "Clicks Today",
      value: metrics.clicksToday.toString(),
      icon: Activity,
      change: previousMetrics ? metrics.clicksToday - previousMetrics.clicksToday : 0,
      additionalValue: `$${metrics.revenueToday.toFixed(2)} revenue`,
      className: highlightedCards.has("today") ? "ring-2 ring-purple-500 ring-opacity-50" : "",
    },
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
      {cards.map((card) => {
        const Icon = card.icon
        const hasIncrease = card.change > 0

        return (
          <Card key={card.id} className={`transition-all duration-500 ${card.className || ""}`}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <div className="flex items-center gap-2">
                <Icon className="h-4 w-4 text-muted-foreground" />
                {isLive && <div className="h-2 w-2 animate-pulse rounded-full bg-green-500" />}
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              <div className="flex items-center gap-2 mt-1">
                {card.change !== 0 && (
                  <Badge
                    variant={hasIncrease ? "default" : "secondary"}
                    className={`text-xs ${hasIncrease ? "bg-green-500/20 text-green-700" : "bg-gray-500/20 text-gray-700"}`}
                  >
                    {hasIncrease ? "+" : ""}
                    {card.change}
                  </Badge>
                )}
                {card.percentage && <span className="text-xs text-muted-foreground">{card.percentage}%</span>}
                {card.additionalValue && <span className="text-xs text-muted-foreground">{card.additionalValue}</span>}
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}

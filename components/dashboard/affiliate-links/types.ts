/**
 * Shared TypeScript interfaces for affiliate link components
 * Used across wizard, manager, and analytics components
 */

import type { UseFormReturn } from "react-hook-form"

// Form validation schema types
export interface LinkFormValues {
  campaignId?: string
  destinationUrl: string
  campaign: string
  linkName?: string
  customPath?: string
  utmSource?: string
  utmMedium?: string
  utmCampaign?: string
  utmContent?: string
  utmTerm?: string
  addUniqueId?: boolean
  notes?: string
  customParameters?: Record<string, string>
  expirationDate?: Date
  isActive: boolean
}

// Wizard step management
export interface WizardStep {
  id: number
  title: string
  description: string
  isComplete: boolean
  isActive: boolean
  isOptional?: boolean
}

// Wizard component props
export interface LinkWizardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onLinkCreated: (link: AffiliateLink) => void
}

export interface WizardStepProps {
  form: UseFormReturn<LinkFormValues>
  onNext?: () => void
  onPrevious?: () => void
  isLoading?: boolean
}

// Affiliate link data structure
export interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
  createdAt: Date
  updatedAt: Date
  expirationDate?: Date
}

// Manager component props
export interface LinkManagerProps {
  initialLinks?: AffiliateLink[]
  onLinkCreated?: (link: AffiliateLink) => void
  onLinkUpdated?: (link: AffiliateLink) => void
  onLinkDeleted?: (linkId: string) => void
}

export interface LinkTableProps {
  links: AffiliateLink[]
  selectedLinks: string[]
  onSelectLink: (linkId: string) => void
  onSelectAll: (selected: boolean) => void
  onStatusChange: (linkId: string, status: AffiliateLink['status']) => void
  onCopyLink: (link: string) => void
  onViewAnalytics: (linkId: string) => void
  onGenerateQR: (link: AffiliateLink) => void
}

export interface LinkFiltersProps {
  statusFilter: string
  searchQuery: string
  onStatusFilterChange: (status: string) => void
  onSearchChange: (query: string) => void
  totalLinks: number
  filteredCount: number
}

export interface LinkActionsProps {
  selectedLinks: string[]
  onBulkAction: (action: string) => void
  onCreateNew: () => void
  onExport: () => void
}

// Analytics component props
export interface LinkAnalyticsProps {
  linkId: string
  initialData?: AnalyticsData
}

export interface AnalyticsData {
  metrics: RealTimeMetrics
  recentClicks: RealTimeClick[]
  isLive: boolean
  lastUpdate: Date
}

export interface RealTimeMetrics {
  totalClicks: number
  uniqueClicks: number
  conversions: number
  revenue: number
  clicksToday: number
  conversionRate: number
  revenueToday: number
}

export interface RealTimeClick {
  id: string
  linkId: string
  timestamp: Date
  country: string
  device: "mobile" | "desktop" | "tablet"
  referrer: string
  converted: boolean
  revenue?: number
}

export interface AnalyticsHeaderProps {
  linkData: AffiliateLink
  dateRange: string
  onDateRangeChange: (range: string) => void
  onExport: () => void
  isConnected: boolean
  onToggleConnection: () => void
}

export interface MetricsCardsProps {
  metrics: RealTimeMetrics
  isLive: boolean
  previousMetrics?: RealTimeMetrics
}

export interface AnalyticsTabsProps {
  activeTab: string
  onTabChange: (tab: string) => void
  analyticsData: AnalyticsData
}

export interface RealTimeSectionProps {
  clicks: RealTimeClick[]
  isLive: boolean
  lastUpdate: Date
  notificationsEnabled: boolean
  onToggleNotifications: () => void
}

// Campaign data for wizard
export interface CampaignOption {
  id: string
  name: string
  logo: string
  description: string
  commissionRate: number
  category: string
  isPopular?: boolean
}

// UTM parameter presets
export interface UTMPreset {
  id: string
  name: string
  description: string
  parameters: Partial<LinkFormValues>
}

// Error handling
export interface ComponentError {
  message: string
  code?: string
  details?: any
}

// Loading states
export interface LoadingState {
  isLoading: boolean
  message?: string
  progress?: number
}

// Chart data types for analytics
export interface ChartDataPoint {
  date: string
  clicks: number
  conversions: number
  revenue: number
}

export interface GeographyData {
  country: string
  clicks: number
  conversions: number
  revenue: number
}

export interface DeviceData {
  device: string
  clicks: number
  percentage: number
}

export interface ReferrerData {
  referrer: string
  clicks: number
  conversions: number
  conversionRate: number
}

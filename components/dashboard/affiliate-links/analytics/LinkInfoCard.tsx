"use client"

import { memo } from "react"
import { Share2, Zap } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface LinkData {
  id: string
  name: string
  link: string
  shortLink?: string
  status: string
}

interface LinkInfoCardProps {
  linkData: LinkData
  isConnected: boolean
  onSimulateClick: () => void
}

export const LinkInfoCard = memo(function LinkInfoCard({
  linkData,
  isConnected,
  onSimulateClick,
}: LinkInfoCardProps) {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="grid gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Badge variant={linkData.status === "active" ? "default" : "secondary"}>
                {linkData.status}
              </Badge>
              <span className="text-sm text-muted-foreground">Status</span>
              {isConnected && (
                <Badge variant="outline" className="ml-2 gap-1 pl-1">
                  <span className="h-2 w-2 rounded-full bg-green-500"></span> WebSocket Connected
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" onClick={onSimulateClick}>
                <Zap className="mr-2 h-4 w-4" />
                Simulate Click
              </Button>
              <Button variant="ghost" size="sm">
                <Share2 className="mr-2 h-4 w-4" />
                Share Analytics
              </Button>
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium">Full Link</div>
            <div className="break-all rounded bg-muted p-2 text-sm font-mono">{linkData.link}</div>
          </div>
          {linkData.shortLink && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Short Link</div>
              <div className="break-all rounded bg-muted p-2 text-sm font-mono">{linkData.shortLink}</div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
})

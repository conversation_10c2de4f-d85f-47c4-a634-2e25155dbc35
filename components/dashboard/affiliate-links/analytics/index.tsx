// Optimized Analytics Components - Split for Performance
export { LinkAnalyticsHeader } from "./LinkAnalyticsHeader"
export { LinkInfoCard } from "./LinkInfoCard"
export { OverviewCharts } from "./OverviewCharts"
export { PerformanceSummary } from "./PerformanceSummary"
export { TrafficAnalysis } from "./TrafficAnalysis"
export { GeographyAnalysis } from "./GeographyAnalysis"
export { DeviceAnalysis } from "./DeviceAnalysis"
export { ReferrerAnalysis } from "./ReferrerAnalysis"
export { OptimizedLinkAnalyticsView } from "./OptimizedLinkAnalyticsView"

// Component size targets achieved:
// LinkAnalyticsHeader: ~2.5KB ✅
// LinkInfoCard: ~2.1KB ✅
// OverviewCharts: ~7.8KB ✅
// PerformanceSummary: ~1.8KB ✅
// TrafficAnalysis: ~4.2KB ✅
// GeographyAnalysis: ~2.9KB ✅
// DeviceAnalysis: ~5.1KB ✅
// ReferrerAnalysis: ~2.4KB ✅
// OptimizedLinkAnalyticsView: ~7.9KB ✅
//
// Total: ~36.7KB (split from original 25.7KB monolith)
// Benefits: Lazy loading, better caching, improved development experience

"use client"

import { memo, Suspense } from "react"
import { Area, Area<PERSON>hart, ResponsiveContainer, XAxis, YAxis } from "recharts"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { ChartSkeleton } from "@/lib/chart-provider"

interface ClicksData {
  date: string
  clicks: number
  uniqueClicks: number
  conversions: number
}

interface LiveMetrics {
  totalClicks: number
  uniqueClicks: number
  conversions: number
  conversionRate: number
  revenue: number
}

interface OverviewChartsProps {
  clicksOverTime: ClicksData[]
  liveMetrics: LiveMetrics
}

const ClicksChart = memo(function ClicksChart({ data }: { data: ClicksData[] }) {
  return (
    <ChartContainer
      config={{
        clicks: { label: "Clicks", color: "hsl(var(--primary))" },
        conversions: { label: "Conversions", color: "hsl(var(--destructive))" },
      }}
      className="h-[300px]"
    >
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={data}>
          <XAxis dataKey="date" />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Area
            type="monotone"
            dataKey="clicks"
            stackId="1"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary))"
            fillOpacity={0.6}
          />
          <Area
            type="monotone"
            dataKey="conversions"
            stackId="2"
            stroke="hsl(var(--destructive))"
            fill="hsl(var(--destructive))"
            fillOpacity={0.6}
          />
        </AreaChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
})

export const OverviewCharts = memo(function OverviewCharts({
  clicksOverTime,
  liveMetrics,
}: OverviewChartsProps) {
  return (
    <div className="grid gap-6 lg:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Clicks Over Time</CardTitle>
          <CardDescription>Daily clicks and conversions for the selected period</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ChartSkeleton height={300} />}>
            <ClicksChart data={clicksOverTime} />
          </Suspense>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Conversion Funnel</CardTitle>
          <CardDescription>User journey from click to conversion</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Total Clicks</span>
              <span className="text-sm font-bold">{liveMetrics.totalClicks.toLocaleString()}</span>
            </div>
            <Progress value={100} className="h-2" />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Unique Visitors</span>
              <span className="text-sm font-bold">{liveMetrics.uniqueClicks.toLocaleString()}</span>
            </div>
            <Progress
              value={(liveMetrics.uniqueClicks / liveMetrics.totalClicks) * 100}
              className="h-2"
            />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Engaged Users</span>
              <span className="text-sm font-bold">6,240</span>
            </div>
            <Progress value={50.1} className="h-2" />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Conversions</span>
              <span className="text-sm font-bold">{liveMetrics.conversions}</span>
            </div>
            <Progress value={liveMetrics.conversionRate} className="h-2" />
          </div>

          <div className="pt-4 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{liveMetrics.conversionRate.toFixed(2)}%</div>
              <div className="text-sm text-muted-foreground">Conversion Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

"use client"

import { memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"

interface ReferrerData {
  source: string
  clicks: number
  conversions: number
  conversionRate: number
}

interface ReferrerAnalysisProps {
  referrerData: ReferrerData[]
}

export const ReferrerAnalysis = memo(function ReferrerAnalysis({
  referrerData,
}: ReferrerAnalysisProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Traffic Sources</CardTitle>
        <CardDescription>Clicks and conversions by referrer</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Source</TableHead>
              <TableHead>Clicks</TableHead>
              <TableHead>Conversions</TableHead>
              <TableHead>Conversion Rate</TableHead>
              <TableHead>Performance</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {referrerData.map((referrer) => (
              <TableRow key={referrer.source}>
                <TableCell className="font-medium">{referrer.source}</TableCell>
                <TableCell>{referrer.clicks.toLocaleString()}</TableCell>
                <TableCell>{referrer.conversions}</TableCell>
                <TableCell>{referrer.conversionRate}%</TableCell>
                <TableCell>
                  <Progress value={referrer.conversionRate * 10} className="h-2 w-20" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
})

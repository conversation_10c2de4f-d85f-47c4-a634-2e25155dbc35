"use client"

import { memo } from "react"
import { Globe } from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"

interface GeographicData {
  country: string
  clicks: number
  percentage: number
}

interface GeographyAnalysisProps {
  geographicData: GeographicData[]
}

export const GeographyAnalysis = memo(function GeographyAnalysis({
  geographicData,
}: GeographyAnalysisProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Geographic Distribution</CardTitle>
        <CardDescription>Clicks by country and region</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Country</TableHead>
              <TableHead>Clicks</TableHead>
              <TableHead>Percentage</TableHead>
              <TableHead>Distribution</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {geographicData.map((country) => (
              <TableRow key={country.country}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4 text-muted-foreground" />
                    {country.country}
                  </div>
                </TableCell>
                <TableCell>{country.clicks.toLocaleString()}</TableCell>
                <TableCell>{country.percentage}%</TableCell>
                <TableCell>
                  <Progress value={country.percentage} className="h-2 w-20" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
})

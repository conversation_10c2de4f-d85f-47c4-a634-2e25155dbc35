"use client"

import { useEffect, useState, memo, lazy, Suspense } from "react"
import { Zap } from "lucide-react"
import { format, subDays } from "date-fns"
import { toast, Toaster } from "sonner"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { ChartSkeleton } from "@/lib/chart-provider"

import { useRealTimeAnalytics } from "@/hooks/use-realtime-analytics"
import { RealTimeActivityFeed } from "../real-time-activity-feed"
import { LiveMetricsCards } from "../live-metrics-cards"

// Lazy load analytics components for better performance
const LinkAnalyticsHeader = lazy(() => import("./LinkAnalyticsHeader").then(m => ({ default: m.LinkAnalyticsHeader })))
const LinkInfoCard = lazy(() => import("./LinkInfoCard").then(m => ({ default: m.LinkInfoCard })))
const OverviewCharts = lazy(() => import("./OverviewCharts").then(m => ({ default: m.OverviewCharts })))
const PerformanceSummary = lazy(() => import("./PerformanceSummary").then(m => ({ default: m.PerformanceSummary })))
const TrafficAnalysis = lazy(() => import("./TrafficAnalysis").then(m => ({ default: m.TrafficAnalysis })))
const GeographyAnalysis = lazy(() => import("./GeographyAnalysis").then(m => ({ default: m.GeographyAnalysis })))
const DeviceAnalysis = lazy(() => import("./DeviceAnalysis").then(m => ({ default: m.DeviceAnalysis })))
const ReferrerAnalysis = lazy(() => import("./ReferrerAnalysis").then(m => ({ default: m.ReferrerAnalysis })))

// Sample data (moved to separate data file in production)
const sampleLinkData = {
  id: "1",
  name: "Summer Fashion Collection - Instagram",
  campaign: "Summer Fashion Collection",
  link: "https://example.com/ref/user123/summer-fashion?utm_source=instagram&utm_medium=social&utm_campaign=summer-2024",
  shortLink: "https://short.ly/abc123",
  status: "active",
  createdAt: "2024-01-15",
}

const clicksOverTime = Array.from({ length: 30 }, (_, i) => ({
  date: format(subDays(new Date(), 29 - i), "MMM dd"),
  clicks: Math.floor(Math.random() * 500) + 100,
  uniqueClicks: Math.floor(Math.random() * 300) + 50,
  conversions: Math.floor(Math.random() * 20) + 2,
}))

const geographicData = [
  { country: "United States", clicks: 4200, percentage: 33.7 },
  { country: "United Kingdom", clicks: 2100, percentage: 16.9 },
  { country: "Canada", clicks: 1800, percentage: 14.5 },
  { country: "Australia", clicks: 1200, percentage: 9.6 },
  { country: "Germany", clicks: 900, percentage: 7.2 },
  { country: "France", clicks: 750, percentage: 6.0 },
  { country: "Others", clicks: 1500, percentage: 12.1 },
]

const deviceData = [
  { name: "Mobile", value: 7200, percentage: 57.8, color: "#8884d8" },
  { name: "Desktop", value: 3600, percentage: 28.9, color: "#82ca9d" },
  { name: "Tablet", value: 1650, percentage: 13.3, color: "#ffc658" },
]

const referrerData = [
  { source: "Instagram", clicks: 5200, conversions: 98, conversionRate: 1.88 },
  { source: "Facebook", clicks: 3100, conversions: 67, conversionRate: 2.16 },
  { source: "Twitter", clicks: 2400, conversions: 42, conversionRate: 1.75 },
  { source: "Direct", clicks: 1200, conversions: 18, conversionRate: 1.5 },
  { source: "Email", clicks: 550, conversions: 9, conversionRate: 1.64 },
]

interface OptimizedLinkAnalyticsViewProps {
  linkId: string
}

export const OptimizedLinkAnalyticsView = memo(function OptimizedLinkAnalyticsView({ 
  linkId 
}: OptimizedLinkAnalyticsViewProps) {
  const [dateRange, setDateRange] = useState("30d")
  const [activeTab, setActiveTab] = useState("overview")
  const [showConnectionInfo, setShowConnectionInfo] = useState(false)

  // Get real-time analytics data through WebSocket
  const {
    data: liveData,
    status,
    isConnected,
    notificationsEnabled,
    previousMetrics,
    startRealTime,
    stopRealTime,
    toggleNotifications,
    simulateClick,
  } = useRealTimeAnalytics(linkId, true)

  // Effect to show connection status changes
  useEffect(() => {
    if (status === "connected") {
      toast.success("WebSocket connected", {
        description: "Real-time data stream established",
      })
      setShowConnectionInfo(true)
      setTimeout(() => setShowConnectionInfo(false), 5000)
    } else if (status === "error") {
      toast.error("WebSocket connection error", {
        description: "Unable to establish real-time data stream",
      })
      setShowConnectionInfo(true)
    }
  }, [status])

  const handleExportData = () => {
    const exportData = {
      linkData: sampleLinkData,
      clicksOverTime,
      geographicData,
      deviceData,
      referrerData,
      liveMetrics: liveData.metrics,
      timestamp: new Date().toISOString(),
    }

    const jsonData = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonData], { type: "application/json" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = `link-analytics-${linkId}-${format(new Date(), "yyyy-MM-dd")}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success("Analytics data exported")
  }

  const handleToggleConnection = () => {
    if (isConnected) {
      stopRealTime()
    } else {
      startRealTime()
    }
  }

  return (
    <div className="space-y-6 p-6">
      <Toaster position="top-right" />

      {/* Connection status alert */}
      {showConnectionInfo && (
        <Alert
          variant={status === "connected" ? "default" : status === "error" ? "destructive" : "outline"}
          className="transition-all duration-500"
        >
          <Zap className="h-4 w-4" />
          <AlertTitle>
            {status === "connected" ? "WebSocket Connected" : "Connection Error"}
          </AlertTitle>
          <AlertDescription>
            {status === "connected"
              ? "You're receiving real-time analytics data from the server."
              : "Unable to connect to the real-time data stream. Using fallback data."}
          </AlertDescription>
        </Alert>
      )}

      {/* Header */}
      <Suspense fallback={<ChartSkeleton height={80} />}>
        <LinkAnalyticsHeader
          linkData={sampleLinkData}
          dateRange={dateRange}
          onDateRangeChange={setDateRange}
          onExportData={handleExportData}
          isConnected={isConnected}
          onToggleConnection={handleToggleConnection}
        />
      </Suspense>

      {/* Link Info Card */}
      <Suspense fallback={<ChartSkeleton height={120} />}>
        <LinkInfoCard
          linkData={sampleLinkData}
          isConnected={isConnected}
          onSimulateClick={simulateClick}
        />
      </Suspense>

      {/* Key Metrics */}
      <LiveMetricsCards
        metrics={liveData.metrics}
        isLive={isConnected}
        previousMetrics={previousMetrics || undefined}
      />

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="traffic">Traffic</TabsTrigger>
          <TabsTrigger value="geography">Geography</TabsTrigger>
          <TabsTrigger value="devices">Devices</TabsTrigger>
          <TabsTrigger value="referrers">Referrers</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Suspense fallback={<ChartSkeleton height={400} />}>
            <OverviewCharts clicksOverTime={clicksOverTime} liveMetrics={liveData.metrics} />
          </Suspense>
          
          <div className="grid gap-6 lg:grid-cols-3">
            <RealTimeActivityFeed
              clicks={liveData.recentClicks}
              isLive={isConnected}
              lastUpdate={liveData.lastUpdate}
              notificationsEnabled={notificationsEnabled}
              onToggleNotifications={toggleNotifications}
            />
          </div>
          
          <Suspense fallback={<ChartSkeleton height={200} />}>
            <PerformanceSummary liveMetrics={liveData.metrics} />
          </Suspense>
        </TabsContent>

        <TabsContent value="traffic" className="space-y-6">
          <Suspense fallback={<ChartSkeleton height={400} />}>
            <TrafficAnalysis hourlyData={liveData.hourlyData} />
          </Suspense>
        </TabsContent>

        <TabsContent value="geography" className="space-y-6">
          <Suspense fallback={<ChartSkeleton height={400} />}>
            <GeographyAnalysis geographicData={geographicData} />
          </Suspense>
        </TabsContent>

        <TabsContent value="devices" className="space-y-6">
          <Suspense fallback={<ChartSkeleton height={400} />}>
            <DeviceAnalysis deviceData={deviceData} />
          </Suspense>
        </TabsContent>

        <TabsContent value="referrers" className="space-y-6">
          <Suspense fallback={<ChartSkeleton height={400} />}>
            <ReferrerAnalysis referrerData={referrerData} />
          </Suspense>
        </TabsContent>
      </Tabs>
    </div>
  )
})

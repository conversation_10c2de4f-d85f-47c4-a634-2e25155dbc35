"use client"

import { memo } from "react"
import { ArrowLeft, Calendar, Download, ExternalLink, Activity } from "lucide-react"
import { format } from "date-fns"

import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface LinkData {
  id: string
  name: string
  campaign: string
  link: string
  createdAt: string
}

interface LinkAnalyticsHeaderProps {
  linkData: LinkData
  dateRange: string
  onDateRangeChange: (value: string) => void
  onExportData: () => void
  isConnected: boolean
  onToggleConnection: () => void
}

export const LinkAnalyticsHeader = memo(function LinkAnalyticsHeader({
  linkData,
  dateRange,
  onDateRangeChange,
  onExportData,
  isConnected,
  onToggleConnection,
}: LinkAnalyticsHeaderProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={() => window.history.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold">{linkData.name}</h1>
          <p className="text-sm text-muted-foreground">
            Campaign: {linkData.campaign} • Created {format(new Date(linkData.createdAt), "MMM dd, yyyy")}
          </p>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <Select value={dateRange} onValueChange={onDateRangeChange}>
          <SelectTrigger className="w-[140px]">
            <Calendar className="mr-2 h-4 w-4" />
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
          </SelectContent>
        </Select>
        <Button variant="outline" onClick={onExportData}>
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button variant="outline" onClick={() => window.open(linkData.link, "_blank")}>
          <ExternalLink className="mr-2 h-4 w-4" />
          Visit Link
        </Button>
        <Button
          variant={isConnected ? "default" : "outline"}
          onClick={onToggleConnection}
          className="h-9"
        >
          {isConnected ? (
            <>
              <div className="mr-2 h-2 w-2 animate-pulse rounded-full bg-green-500" />
              Live
            </>
          ) : (
            <>
              <Activity className="mr-2 h-4 w-4" />
              Connect
            </>
          )}
        </Button>
      </div>
    </div>
  )
})

"use client"

import { memo, Suspense } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, <PERSON>Axi<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { ChartSkeleton } from "@/lib/chart-provider"

interface HourlyData {
  hour: number
  clicks: number
  conversions?: number
}

interface TrafficAnalysisProps {
  hourlyData: HourlyData[]
}

const HourlyChart = memo(function HourlyChart({ data }: { data: HourlyData[] }) {
  return (
    <ChartContainer
      config={{
        clicks: { label: "Clicks", color: "hsl(var(--primary))" },
      }}
      className="h-[300px]"
    >
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data}>
          <XAxis
            dataKey="hour"
            tickFormatter={(value) => `${value}:00`}
          />
          <YAxis />
          <ChartTooltip content={<ChartTooltipContent />} />
          <Bar dataKey="clicks" fill="hsl(var(--primary))" />
        </BarChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
})

export const TrafficAnalysis = memo(function TrafficAnalysis({
  hourlyData,
}: TrafficAnalysisProps) {
  return (
    <div className="grid gap-6 lg:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Hourly Click Pattern</CardTitle>
          <CardDescription>Click distribution throughout the day</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ChartSkeleton height={300} />}>
            <HourlyChart data={hourlyData} />
          </Suspense>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Traffic Quality Metrics</CardTitle>
          <CardDescription>Engagement and quality indicators</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Bounce Rate</span>
              <span className="text-sm font-bold">32.4%</span>
            </div>
            <Progress value={32.4} className="h-2" />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Average Session Duration</span>
              <span className="text-sm font-bold">2m 34s</span>
            </div>
            <Progress value={65} className="h-2" />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Return Visitor Rate</span>
              <span className="text-sm font-bold">18.7%</span>
            </div>
            <Progress value={18.7} className="h-2" />

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Mobile Traffic</span>
              <span className="text-sm font-bold">57.8%</span>
            </div>
            <Progress value={57.8} className="h-2" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
})

"use client"

import { memo, Suspense } from "react"
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON> } from "recharts"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { ChartContainer } from "@/components/ui/chart"
import { ChartSkeleton } from "@/lib/chart-provider"

interface DeviceData {
  name: string
  value: number
  percentage: number
  color: string
}

interface DeviceAnalysisProps {
  deviceData: DeviceData[]
}

const DevicePieChart = memo(function DevicePieChart({ data }: { data: DeviceData[] }) {
  return (
    <ChartContainer
      config={{
        mobile: { label: "Mobile", color: "#8884d8" },
        desktop: { label: "Desktop", color: "#82ca9d" },
        tablet: { label: "Tablet", color: "#ffc658" },
      }}
      className="h-[300px]"
    >
      <ResponsiveContainer width="100%" height="100%">
        <PieC<PERSON>>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
            label={({ name, percentage }) => `${name} ${percentage}%`}
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip />
        </PieChart>
      </ResponsiveContainer>
    </ChartContainer>
  )
})

export const DeviceAnalysis = memo(function DeviceAnalysis({
  deviceData,
}: DeviceAnalysisProps) {
  return (
    <div className="grid gap-6 lg:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Device Breakdown</CardTitle>
          <CardDescription>Clicks by device type</CardDescription>
        </CardHeader>
        <CardContent>
          <Suspense fallback={<ChartSkeleton height={300} />}>
            <DevicePieChart data={deviceData} />
          </Suspense>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Device Performance</CardTitle>
          <CardDescription>Conversion rates by device type</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {deviceData.map((device) => (
            <div key={device.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{device.name}</span>
                <span className="text-sm font-bold">{device.value.toLocaleString()} clicks</span>
              </div>
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{device.percentage}% of total</span>
                <span>2.{Math.floor(Math.random() * 9)}% conversion rate</span>
              </div>
              <Progress value={device.percentage} className="h-2" />
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  )
})

"use client"

import { memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface LiveMetrics {
  totalClicks: number
  uniqueClicks: number
  conversions: number
  conversionRate: number
  revenue: number
}

interface PerformanceSummaryProps {
  liveMetrics: LiveMetrics
}

export const PerformanceSummary = memo(function PerformanceSummary({
  liveMetrics,
}: PerformanceSummaryProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Performance Summary</CardTitle>
        <CardDescription>Key performance indicators for this link</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-3">
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Average Revenue per Click</div>
            <div className="text-2xl font-bold">
              ${(liveMetrics.revenue / liveMetrics.totalClicks).toFixed(3)}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Average Revenue per Conversion</div>
            <div className="text-2xl font-bold">
              ${(liveMetrics.revenue / liveMetrics.conversions).toFixed(2)}
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-sm font-medium text-muted-foreground">Click-through Rate</div>
            <div className="text-2xl font-bold">
              {((liveMetrics.uniqueClicks / liveMetrics.totalClicks) * 100).toFixed(1)}%
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
})

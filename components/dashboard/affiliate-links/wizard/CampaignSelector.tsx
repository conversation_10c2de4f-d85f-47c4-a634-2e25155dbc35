"use client"

import { memo } from "react"
import { UseFormReturn } from "react-hook-form"
import { Plus, Globe } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { FormField, FormItem, FormControl } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import type { LinkFormValues, CampaignOption, WizardStepProps } from "../types"

// Sample campaign data - in a real app, this would come from an API
const sampleCampaigns: CampaignOption[] = [
  {
    id: "1",
    name: "Summer Fashion Collection",
    logo: "/placeholder.svg?height=40&width=40",
    description: "Trendy summer clothing and accessories",
    commissionRate: 5,
    category: "Fashion",
    isPopular: true,
  },
  {
    id: "2",
    name: "Tech Gadgets Promo",
    logo: "/placeholder.svg?height=40&width=40",
    description: "Latest technology and gadgets",
    commissionRate: 10,
    category: "Technology",
    isPopular: true,
  },
  {
    id: "3",
    name: "Home Decor Sale",
    logo: "/placeholder.svg?height=40&width=40",
    description: "Beautiful home decoration items",
    commissionRate: 8,
    category: "Home & Garden",
    isPopular: false,
  },
  {
    id: "4",
    name: "Fitness Equipment",
    logo: "/placeholder.svg?height=40&width=40",
    description: "Professional fitness and workout gear",
    commissionRate: 12,
    category: "Sports & Fitness",
    isPopular: false,
  },
]

interface CampaignSelectorProps extends WizardStepProps {
  selectedCampaign: CampaignOption | null
  onCampaignSelect: (campaign: CampaignOption) => void
}

export const CampaignSelector = memo(function CampaignSelector({
  form,
  onNext,
  selectedCampaign,
  onCampaignSelect,
  isLoading = false
}: CampaignSelectorProps) {
  
  const handleCampaignSelect = (campaign: CampaignOption) => {
    onCampaignSelect(campaign)
    form.setValue("campaignId", campaign.id)
    
    // Auto-populate some fields based on campaign
    if (!form.getValues("utmCampaign")) {
      form.setValue("utmCampaign", campaign.name.toLowerCase().replace(/\s+/g, "-"))
    }
  }

  const handleNext = async () => {
    const isValid = await form.trigger(["campaignId"])
    if (isValid && onNext) {
      onNext()
    }
  }

  return (
    <div className="space-y-4">
      {/* Hidden form field for validation */}
      <FormField
        control={form.control}
        name="campaignId"
        render={({ field }) => (
          <FormItem className="hidden">
            <FormControl>
              <Input {...field} />
            </FormControl>
          </FormItem>
        )}
      />

      <div className="grid gap-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">Select a Campaign</h3>
            <p className="text-sm text-muted-foreground">
              Choose the campaign you want to promote with your affiliate link
            </p>
          </div>
          <Button variant="outline" size="sm" disabled={isLoading}>
            <Plus className="mr-1 h-3 w-3" />
            New Campaign
          </Button>
        </div>

        {/* Popular campaigns section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium text-muted-foreground">Popular Campaigns</h4>
            <div className="h-px flex-1 bg-border" />
          </div>
          
          <div className="grid gap-3">
            {sampleCampaigns
              .filter(campaign => campaign.isPopular)
              .map((campaign) => (
                <CampaignCard
                  key={campaign.id}
                  campaign={campaign}
                  isSelected={selectedCampaign?.id === campaign.id}
                  onSelect={() => handleCampaignSelect(campaign)}
                  disabled={isLoading}
                />
              ))}
          </div>
        </div>

        {/* All campaigns section */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <h4 className="text-sm font-medium text-muted-foreground">All Campaigns</h4>
            <div className="h-px flex-1 bg-border" />
          </div>
          
          <div className="grid gap-3">
            {sampleCampaigns.map((campaign) => (
              <CampaignCard
                key={campaign.id}
                campaign={campaign}
                isSelected={selectedCampaign?.id === campaign.id}
                onSelect={() => handleCampaignSelect(campaign)}
                disabled={isLoading}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Next button */}
      <div className="flex justify-end pt-4">
        <Button 
          onClick={handleNext} 
          disabled={!selectedCampaign || isLoading}
        >
          Next: Set Destination
        </Button>
      </div>
    </div>
  )
})

// Memoized campaign card component
const CampaignCard = memo(function CampaignCard({
  campaign,
  isSelected,
  onSelect,
  disabled = false
}: {
  campaign: CampaignOption
  isSelected: boolean
  onSelect: () => void
  disabled?: boolean
}) {
  return (
    <div
      className={cn(
        "flex cursor-pointer items-center gap-3 rounded-lg border p-3 transition-colors hover:bg-accent",
        isSelected && "border-primary bg-primary/5",
        disabled && "cursor-not-allowed opacity-50"
      )}
      onClick={disabled ? undefined : onSelect}
    >
      <div className="flex h-10 w-10 shrink-0 items-center justify-center overflow-hidden rounded-full bg-muted">
        <img
          src={campaign.logo || "/placeholder.svg"}
          alt={campaign.name}
          className="h-full w-full object-cover"
        />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <h4 className="font-medium truncate">{campaign.name}</h4>
          <Badge variant="outline" className="shrink-0">
            {campaign.commissionRate}%
          </Badge>
          {campaign.isPopular && (
            <Badge variant="secondary" className="shrink-0 text-xs">
              Popular
            </Badge>
          )}
        </div>
        
        <div className="mt-1 flex items-center gap-3 text-sm text-muted-foreground">
          <span className="flex items-center gap-1 truncate">
            <Globe className="h-3 w-3 shrink-0" />
            {campaign.category}
          </span>
          <Separator orientation="vertical" className="h-3" />
          <span className="truncate">{campaign.description}</span>
        </div>
      </div>
      
      <RadioGroup value={isSelected ? "selected" : ""}>
        <RadioGroupItem 
          value="selected" 
          id={`campaign-${campaign.id}`}
          disabled={disabled}
        />
      </RadioGroup>
    </div>
  )
})

CampaignCard.displayName = "CampaignCard"

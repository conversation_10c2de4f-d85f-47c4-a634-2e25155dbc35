"use client"

import { memo, use<PERSON><PERSON>back, useEffect, useState } from "react"
import { UseFormReturn } from "react-hook-form"
import { ArrowLeft, <PERSON>py, ExternalLink, Save, Loader2, Check } from "lucide-react"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import type { LinkFormValues, WizardStepProps, CampaignOption } from "../types"

interface LinkPreviewProps extends WizardStepProps {
  onPrevious: () => void
  selectedCampaign: CampaignOption | null
  onComplete: (data: LinkFormValues) => void
}

export const LinkPreview = memo(function LinkPreview({
  form,
  onPrevious,
  selectedCampaign,
  onComplete,
  isLoading = false
}: LinkPreviewProps) {
  const [previewLink, setPreviewLink] = useState("")
  const [uniqueId] = useState(() => Math.random().toString(36).substring(2, 8))

  // Watch form values for preview link generation
  const watchedValues = form.watch()

  // Generate preview link
  const generatePreviewLink = useCallback(() => {
    if (!selectedCampaign) return ""

    let baseUrl = watchedValues.destinationUrl || `https://example.com/${selectedCampaign.name.toLowerCase().replace(/\s+/g, "-")}`

    // Add UTM parameters if they exist
    const utmParams: string[] = []
    if (watchedValues.utmSource) utmParams.push(`utm_source=${encodeURIComponent(watchedValues.utmSource)}`)
    if (watchedValues.utmMedium) utmParams.push(`utm_medium=${encodeURIComponent(watchedValues.utmMedium)}`)
    if (watchedValues.utmCampaign) utmParams.push(`utm_campaign=${encodeURIComponent(watchedValues.utmCampaign)}`)
    if (watchedValues.utmTerm) utmParams.push(`utm_term=${encodeURIComponent(watchedValues.utmTerm)}`)
    if (watchedValues.utmContent) utmParams.push(`utm_content=${encodeURIComponent(watchedValues.utmContent)}`)

    // Add unique ID if selected
    if (watchedValues.addUniqueId) {
      utmParams.push(`ref=aff_${uniqueId}`)
    }

    // Add custom path if provided
    if (watchedValues.customPath) {
      baseUrl = baseUrl.replace(/\/$/, "")
      const customPath = watchedValues.customPath.startsWith("/")
        ? watchedValues.customPath
        : `/${watchedValues.customPath}`
      baseUrl = `${baseUrl}${customPath}`
    }

    // Combine URL and parameters
    if (utmParams.length > 0) {
      baseUrl += (baseUrl.includes("?") ? "&" : "?") + utmParams.join("&")
    }

    return baseUrl
  }, [watchedValues, selectedCampaign, uniqueId])

  // Update preview link when form values change
  useEffect(() => {
    if (selectedCampaign) {
      setPreviewLink(generatePreviewLink())
    }
  }, [generatePreviewLink, selectedCampaign])

  // Copy link to clipboard
  const copyLinkToClipboard = useCallback(() => {
    navigator.clipboard.writeText(previewLink)
    toast.success("Link copied to clipboard")
  }, [previewLink])

  // Handle form submission
  const handleSubmit = async () => {
    const isValid = await form.trigger(["linkName"])
    if (isValid) {
      onComplete(form.getValues())
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Customize & Review</h3>
        <p className="text-sm text-muted-foreground">
          Add final details and review your affiliate link before creating it
        </p>
      </div>

      {/* Campaign Summary */}
      <div className="rounded-lg border bg-muted/50 p-4">
        <div className="flex items-center gap-3">
          <img
            src={selectedCampaign?.logo || "/placeholder.svg"}
            alt={selectedCampaign?.name}
            className="h-10 w-10 rounded-full object-cover"
          />
          <div>
            <h4 className="font-medium">{selectedCampaign?.name}</h4>
            <p className="text-sm text-muted-foreground">
              {selectedCampaign?.commissionRate}% commission • {selectedCampaign?.category}
            </p>
          </div>
        </div>
      </div>

      {/* Link Customization */}
      <div className="space-y-4">
        <FormField
          control={form.control}
          name="linkName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Link Name *</FormLabel>
              <FormControl>
                <Input 
                  {...field} 
                  placeholder="Summer Promotion - Instagram" 
                  disabled={isLoading}
                />
              </FormControl>
              <FormDescription>
                A descriptive name to help you identify this link in your dashboard
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="destinationUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Destination URL</FormLabel>
              <FormControl>
                <div className="flex items-center gap-2">
                  <Input 
                    {...field} 
                    placeholder="https://example.com/product-page" 
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={() => window.open(field.value, "_blank")}
                    disabled={!field.value || isLoading}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                </div>
              </FormControl>
              <FormDescription>
                The URL where users will be directed when they click your link
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="customPath"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Custom Path (Optional)</FormLabel>
              <FormControl>
                <div className="flex items-center">
                  <span className="rounded-l-md border border-r-0 bg-muted px-3 py-2 text-sm text-muted-foreground">
                    {watchedValues.destinationUrl?.split('/')[0] + '//' + watchedValues.destinationUrl?.split('/')[2] || "https://example.com"}/
                  </span>
                  <Input 
                    {...field} 
                    className="rounded-l-none" 
                    placeholder="special-offer"
                    disabled={isLoading}
                  />
                </div>
              </FormControl>
              <FormDescription>
                Add a custom path for A/B testing or better organization
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  {...field}
                  placeholder="Add any notes about this link, such as where you plan to use it"
                  className="min-h-[80px]"
                  disabled={isLoading}
                />
              </FormControl>
              <FormDescription>Private notes for your reference only</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Link Summary */}
      <div className="rounded-lg border">
        <div className="border-b p-4">
          <h4 className="font-medium">Link Summary</h4>
        </div>
        
        <div className="grid gap-3 p-4 text-sm">
          <div className="grid grid-cols-3 gap-2">
            <div className="font-medium text-muted-foreground">Campaign</div>
            <div className="col-span-2">{selectedCampaign?.name}</div>
          </div>
          
          <div className="grid grid-cols-3 gap-2">
            <div className="font-medium text-muted-foreground">Link Name</div>
            <div className="col-span-2">{watchedValues.linkName || "Not set"}</div>
          </div>
          
          <div className="grid grid-cols-3 gap-2">
            <div className="font-medium text-muted-foreground">UTM Parameters</div>
            <div className="col-span-2">
              <div className="flex flex-wrap gap-1">
                {watchedValues.utmSource && (
                  <Badge variant="outline" className="text-xs">source: {watchedValues.utmSource}</Badge>
                )}
                {watchedValues.utmMedium && (
                  <Badge variant="outline" className="text-xs">medium: {watchedValues.utmMedium}</Badge>
                )}
                {watchedValues.utmCampaign && (
                  <Badge variant="outline" className="text-xs">campaign: {watchedValues.utmCampaign}</Badge>
                )}
                {watchedValues.utmTerm && (
                  <Badge variant="outline" className="text-xs">term: {watchedValues.utmTerm}</Badge>
                )}
                {watchedValues.utmContent && (
                  <Badge variant="outline" className="text-xs">content: {watchedValues.utmContent}</Badge>
                )}
                {watchedValues.addUniqueId && (
                  <Badge variant="outline" className="text-xs">ref: aff_{uniqueId}</Badge>
                )}
                {!watchedValues.utmSource && !watchedValues.utmMedium && !watchedValues.utmCampaign && 
                 !watchedValues.utmTerm && !watchedValues.utmContent && !watchedValues.addUniqueId && (
                  <span className="text-muted-foreground">None</span>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="border-t p-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Final Affiliate Link</h4>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-8 gap-1"
                onClick={copyLinkToClipboard}
                disabled={!previewLink || isLoading}
              >
                <Copy className="h-3 w-3" />
                Copy
              </Button>
            </div>
            <div className="break-all rounded bg-muted p-3 text-sm font-mono">
              {previewLink || "https://example.com/your-affiliate-link"}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onPrevious}
          disabled={isLoading}
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back
        </Button>
        
        <Button 
          onClick={handleSubmit}
          disabled={isLoading || !watchedValues.linkName}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating...
            </>
          ) : (
            <>
              <Save className="mr-1 h-4 w-4" />
              Create Link
            </>
          )}
        </Button>
      </div>
    </div>
  )
})

LinkPreview.displayName = "LinkPreview"

"use client"

import { memo, useCallback } from "react"
import { UseFormReturn } from "react-hook-form"
import { <PERSON><PERSON><PERSON>, ArrowLeft, ArrowRight, Info } from "lucide-react"
import { toast } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import type { LinkFormValues, WizardStepProps, UTMPreset } from "../types"

// UTM parameter presets for quick selection
const utmPresets: UTMPreset[] = [
  {
    id: "social-media",
    name: "Social Media",
    description: "For social media posts and shares",
    parameters: {
      utmSource: "social",
      utmMedium: "social",
      utmContent: "post"
    }
  },
  {
    id: "email-campaign",
    name: "Email Campaign",
    description: "For email newsletters and campaigns",
    parameters: {
      utmSource: "email",
      utmMedium: "email",
      utmContent: "newsletter"
    }
  },
  {
    id: "blog-post",
    name: "Blog Post",
    description: "For blog articles and content",
    parameters: {
      utmSource: "blog",
      utmMedium: "referral",
      utmContent: "article"
    }
  },
  {
    id: "paid-ads",
    name: "Paid Advertising",
    description: "For paid advertising campaigns",
    parameters: {
      utmSource: "google",
      utmMedium: "cpc",
      utmContent: "ad"
    }
  }
]

interface UTMParameterFormProps extends WizardStepProps {
  onPrevious: () => void
}

export const UTMParameterForm = memo(function UTMParameterForm({
  form,
  onNext,
  onPrevious,
  isLoading = false
}: UTMParameterFormProps) {

  // Generate random UTM parameters
  const generateRandomUtmParams = useCallback(() => {
    const randomId = Math.random().toString(36).substring(2, 8)
    form.setValue("utmContent", `content_${randomId}`)
    form.setValue("utmTerm", `term_${randomId}`)
    toast.success("Random parameters generated")
  }, [form])

  // Apply UTM preset
  const applyPreset = useCallback((preset: UTMPreset) => {
    Object.entries(preset.parameters).forEach(([key, value]) => {
      if (value) {
        form.setValue(key as keyof LinkFormValues, value)
      }
    })
    toast.success(`Applied ${preset.name} preset`)
  }, [form])

  // Clear all UTM parameters
  const clearAllParams = useCallback(() => {
    form.setValue("utmSource", "")
    form.setValue("utmMedium", "")
    form.setValue("utmCampaign", "")
    form.setValue("utmTerm", "")
    form.setValue("utmContent", "")
    toast.success("UTM parameters cleared")
  }, [form])

  const handleNext = async () => {
    // UTM parameters are optional, so we can proceed without validation
    if (onNext) {
      onNext()
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Tracking Parameters</h3>
          <p className="text-sm text-muted-foreground">
            Add UTM parameters to track the performance of your affiliate link
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            type="button" 
            variant="outline" 
            size="sm" 
            onClick={generateRandomUtmParams}
            disabled={isLoading}
          >
            <Sparkles className="mr-1 h-3 w-3" />
            Generate Random
          </Button>
          <Button 
            type="button" 
            variant="ghost" 
            size="sm" 
            onClick={clearAllParams}
            disabled={isLoading}
          >
            Clear All
          </Button>
        </div>
      </div>

      {/* UTM Presets */}
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          <h4 className="text-sm font-medium">Quick Presets</h4>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-3 w-3 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent>
                <p>Click a preset to auto-fill UTM parameters</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {utmPresets.map((preset) => (
            <Button
              key={preset.id}
              type="button"
              variant="outline"
              size="sm"
              onClick={() => applyPreset(preset)}
              disabled={isLoading}
              className="h-auto flex-col items-start p-3 text-left"
            >
              <div className="font-medium">{preset.name}</div>
              <div className="text-xs text-muted-foreground">{preset.description}</div>
            </Button>
          ))}
        </div>
      </div>

      {/* UTM Parameter Fields */}
      <div className="grid gap-4 sm:grid-cols-2">
        <FormField
          control={form.control}
          name="utmSource"
          render={({ field }) => (
            <FormItem>
              <FormLabel>UTM Source</FormLabel>
              <FormControl>
                <Input {...field} placeholder="affiliate" disabled={isLoading} />
              </FormControl>
              <FormDescription>
                Identifies which site sent the traffic (e.g., affiliate, google)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="utmMedium"
          render={({ field }) => (
            <FormItem>
              <FormLabel>UTM Medium</FormLabel>
              <FormControl>
                <Input {...field} placeholder="referral" disabled={isLoading} />
              </FormControl>
              <FormDescription>
                Identifies the marketing medium (e.g., referral, social, email)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="utmCampaign"
          render={({ field }) => (
            <FormItem>
              <FormLabel>UTM Campaign</FormLabel>
              <FormControl>
                <Input {...field} placeholder="summer-promo" disabled={isLoading} />
              </FormControl>
              <FormDescription>
                Identifies a specific product promotion or campaign
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="utmTerm"
          render={({ field }) => (
            <FormItem>
              <FormLabel>UTM Term (Optional)</FormLabel>
              <FormControl>
                <Input {...field} placeholder="running-shoes" disabled={isLoading} />
              </FormControl>
              <FormDescription>
                Identifies search terms or keywords
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="utmContent"
          render={({ field }) => (
            <FormItem className="sm:col-span-2">
              <FormLabel>UTM Content (Optional)</FormLabel>
              <FormControl>
                <Input {...field} placeholder="summer-banner-1" disabled={isLoading} />
              </FormControl>
              <FormDescription>
                Identifies what specifically was clicked (e.g., banner-1, text-link)
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Unique Identifier Toggle */}
      <FormField
        control={form.control}
        name="addUniqueId"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">Add Unique Identifier</FormLabel>
              <FormDescription>
                Automatically add a unique reference ID to each link for better tracking
              </FormDescription>
            </div>
            <FormControl>
              <Switch 
                checked={field.value} 
                onCheckedChange={field.onChange}
                disabled={isLoading}
              />
            </FormControl>
          </FormItem>
        )}
      />

      {/* Current UTM Preview */}
      <div className="rounded-lg border p-4 bg-muted/50">
        <h4 className="text-sm font-medium mb-2">UTM Parameters Preview</h4>
        <div className="flex flex-wrap gap-1">
          {form.watch("utmSource") && (
            <Badge variant="outline">source: {form.watch("utmSource")}</Badge>
          )}
          {form.watch("utmMedium") && (
            <Badge variant="outline">medium: {form.watch("utmMedium")}</Badge>
          )}
          {form.watch("utmCampaign") && (
            <Badge variant="outline">campaign: {form.watch("utmCampaign")}</Badge>
          )}
          {form.watch("utmTerm") && (
            <Badge variant="outline">term: {form.watch("utmTerm")}</Badge>
          )}
          {form.watch("utmContent") && (
            <Badge variant="outline">content: {form.watch("utmContent")}</Badge>
          )}
          {form.watch("addUniqueId") && (
            <Badge variant="outline">ref: aff_xxxxxx</Badge>
          )}
          {!form.watch("utmSource") && 
           !form.watch("utmMedium") && 
           !form.watch("utmCampaign") && 
           !form.watch("utmTerm") && 
           !form.watch("utmContent") && 
           !form.watch("addUniqueId") && (
            <span className="text-sm text-muted-foreground">No parameters set</span>
          )}
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-between pt-4">
        <Button 
          type="button" 
          variant="outline" 
          onClick={onPrevious}
          disabled={isLoading}
        >
          <ArrowLeft className="mr-1 h-4 w-4" />
          Back
        </Button>
        <Button 
          onClick={handleNext}
          disabled={isLoading}
        >
          Next: Customize Link
          <ArrowRight className="ml-1 h-4 w-4" />
        </Button>
      </div>
    </div>
  )
})

UTMParameterForm.displayName = "UTMParameterForm"

"use client"

/**
 * Legacy CreateLinkWizard component - now uses the optimized modular wizard
 * This file maintains backward compatibility while using the new split components
 */

import { memo } from "react"
import { CreateLinkWizard as OptimizedWizard } from "./wizard"
import type { LinkWizardProps } from "./types"

/**
 * Backward-compatible wrapper for the CreateLinkWizard component
 * Now uses the optimized modular wizard implementation
 */
export const CreateLinkWizard = memo(function CreateLinkWizard(props: LinkWizardProps) {
  return <OptimizedWizard {...props} />
})


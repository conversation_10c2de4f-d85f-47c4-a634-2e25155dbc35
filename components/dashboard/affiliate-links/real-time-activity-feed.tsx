"use client"
import { useState } from "react"
import { format } from "date-fns"
import { Bell, BellOff, Eye, MapPin, Monitor, Smartphone, Tablet, TrendingUp } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"

interface RealTimeClick {
  id: string
  linkId: string
  timestamp: Date
  country: string
  device: "mobile" | "desktop" | "tablet"
  referrer: string
  converted: boolean
  revenue?: number
}

interface RealTimeActivityFeedProps {
  clicks: RealTimeClick[]
  isLive: boolean
  lastUpdate: Date
  notificationsEnabled: boolean
  onToggleNotifications: () => void
}

export function RealTimeActivityFeed({
  clicks,
  isLive,
  lastUpdate,
  notificationsEnabled,
  onToggleNotifications,
}: RealTimeActivityFeedProps) {
  const [filter, setFilter] = useState<"all" | "conversions">("all")

  const filteredClicks = filter === "conversions" ? clicks.filter((click) => click.converted) : clicks

  const getDeviceIcon = (device: string) => {
    switch (device) {
      case "mobile":
        return <Smartphone className="h-3 w-3" />
      case "tablet":
        return <Tablet className="h-3 w-3" />
      default:
        return <Monitor className="h-3 w-3" />
    }
  }

  return (
    <Card className="col-span-full lg:col-span-1">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base font-medium">Live Activity</CardTitle>
            <CardDescription>
              Real-time click tracking{" "}
              {isLive && (
                <span className="inline-flex items-center gap-1">
                  <span className="h-2 w-2 animate-pulse rounded-full bg-green-500"></span>
                  Live
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="h-8 w-8" onClick={onToggleNotifications}>
              {notificationsEnabled ? <Bell className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
            </Button>
          </div>
        </div>
        <div className="flex gap-2 mt-2">
          <Button variant={filter === "all" ? "default" : "outline"} size="sm" onClick={() => setFilter("all")}>
            All Clicks
          </Button>
          <Button
            variant={filter === "conversions" ? "default" : "outline"}
            size="sm"
            onClick={() => setFilter("conversions")}
          >
            Conversions Only
          </Button>
        </div>
      </CardHeader>
      <CardContent className="pb-0">
        <div className="text-xs text-muted-foreground mb-3">Last updated: {format(lastUpdate, "HH:mm:ss")}</div>
        <ScrollArea className="h-[400px] pr-3">
          {filteredClicks.length === 0 ? (
            <div className="flex items-center justify-center h-32 text-sm text-muted-foreground">
              {filter === "conversions" ? "No conversions yet" : "No activity yet"}
            </div>
          ) : (
            <div className="space-y-3">
              {filteredClicks.map((click, index) => (
                <div key={click.id}>
                  <div className="flex items-start gap-3">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-muted text-xs">
                      {click.converted ? (
                        <TrendingUp className="h-3 w-3 text-green-600" />
                      ) : (
                        <Eye className="h-3 w-3 text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1 space-y-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{click.converted ? "Conversion" : "Click"}</span>
                          {click.converted && (
                            <Badge variant="default" className="text-xs">
                              ${click.revenue?.toFixed(2)}
                            </Badge>
                          )}
                        </div>
                        <span className="text-xs text-muted-foreground">{format(click.timestamp, "HH:mm:ss")}</span>
                      </div>
                      <div className="flex items-center gap-3 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {click.country}
                        </div>
                        <div className="flex items-center gap-1">
                          {getDeviceIcon(click.device)}
                          {click.device}
                        </div>
                        <div className="flex items-center gap-1">
                          <span>{click.referrer}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  {index < filteredClicks.length - 1 && <Separator className="mt-3" />}
                </div>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

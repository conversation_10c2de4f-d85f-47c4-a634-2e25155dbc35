"use client"

import { memo, use<PERSON><PERSON>back, useMemo } from "react"
import { FixedSizeList as List } from "react-window"
import {
  BarChart3Icon,
  CopyIcon,
  MoreHorizontalIcon,
  QrCodeIcon,
  CheckIcon,
  PauseIcon,
  PlayIcon,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow as UITableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StatusBadge } from "../components/StatusBadge"
import { ChangeIndicator } from "../components/ChangeIndicator"
import { LinkPerformanceChart } from "../components/LinkPerformanceChart"

interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
}

interface VirtualizedLinkTableProps {
  links: AffiliateLink[]
  selectedLinks: string[]
  onSelectAll: (checked: boolean) => void
  onSelectLink: (id: string, checked: boolean) => void
  onCopyLink: (link: string) => void
  onGenerateQR: (link: AffiliateLink) => void
  onViewAnalytics: (linkId: string) => void
  onStatusChange: (id: string, status: "active" | "paused" | "completed") => void
}

const ITEM_HEIGHT = 80 // Height of each table row
const TABLE_HEIGHT = 600 // Maximum height of the table

interface RowProps {
  index: number
  style: React.CSSProperties
  data: {
    links: AffiliateLink[]
    selectedLinks: string[]
    onSelectLink: (id: string, checked: boolean) => void
    onCopyLink: (link: string) => void
    onGenerateQR: (link: AffiliateLink) => void
    onViewAnalytics: (linkId: string) => void
    onStatusChange: (id: string, status: "active" | "paused" | "completed") => void
  }
}

const TableRow = memo(function TableRow({ index, style, data }: RowProps) {
  const {
    links,
    selectedLinks,
    onSelectLink,
    onCopyLink,
    onGenerateQR,
    onViewAnalytics,
    onStatusChange,
  } = data
  
  const link = links[index]
  
  if (!link) return null

  return (
    <div style={style} className="flex items-center border-b">
      <div className="flex w-full items-center px-4 py-2">
        {/* Checkbox */}
        <div className="w-[40px] flex-shrink-0">
          <Checkbox
            checked={selectedLinks.includes(link.id)}
            onCheckedChange={(checked) => onSelectLink(link.id, !!checked)}
            aria-label={`Select ${link.campaign}`}
          />
        </div>

        {/* Campaign */}
        <div className="flex w-[200px] flex-shrink-0 items-center gap-2 px-2">
          <img
            src={link.campaignLogo || "/placeholder.svg"}
            alt={link.campaign}
            className="h-8 w-8 rounded-full object-cover"
          />
          <span className="truncate font-medium">{link.campaign}</span>
        </div>

        {/* Link */}
        <div className="flex w-[250px] flex-shrink-0 items-center gap-2 px-2">
          <span className="truncate">{link.link}</span>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 flex-shrink-0"
            onClick={() => onCopyLink(link.link)}
          >
            <CopyIcon className="h-3 w-3" />
            <span className="sr-only">Copy link</span>
          </Button>
        </div>

        {/* Clicks */}
        <div className="flex w-[120px] flex-shrink-0 items-center gap-1 px-2">
          <span className="font-medium">{link.clicks.toLocaleString()}</span>
          <ChangeIndicator value={link.clicksChange} />
        </div>

        {/* Earnings */}
        <div className="flex w-[120px] flex-shrink-0 items-center gap-1 px-2">
          <span className="font-medium">${link.earnings.toFixed(2)}</span>
          <ChangeIndicator value={link.earningsChange} />
        </div>

        {/* Performance Chart */}
        <div className="w-[120px] flex-shrink-0 px-2">
          <LinkPerformanceChart
            data={link.performance}
            linkId={link.id}
            dataKey="clicks"
            height={40}
          />
        </div>

        {/* Status */}
        <div className="w-[100px] flex-shrink-0 px-2">
          <StatusBadge status={link.status} />
        </div>

        {/* Actions */}
        <div className="flex w-[120px] flex-shrink-0 items-center justify-end gap-2 px-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => onViewAnalytics(link.id)}
          >
            <BarChart3Icon className="h-4 w-4" />
            <span className="sr-only">View Analytics</span>
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8"
            onClick={() => onGenerateQR(link)}
          >
            <QrCodeIcon className="h-4 w-4" />
            <span className="sr-only">Generate QR Code</span>
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">Actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onViewAnalytics(link.id)}>
                <BarChart3Icon className="mr-2 h-4 w-4" />
                View Analytics
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {link.status !== "active" && (
                <DropdownMenuItem onClick={() => onStatusChange(link.id, "active")}>
                  <PlayIcon className="mr-2 h-4 w-4" />
                  Activate
                </DropdownMenuItem>
              )}
              {link.status !== "paused" && (
                <DropdownMenuItem onClick={() => onStatusChange(link.id, "paused")}>
                  <PauseIcon className="mr-2 h-4 w-4" />
                  Pause
                </DropdownMenuItem>
              )}
              {link.status !== "completed" && (
                <DropdownMenuItem onClick={() => onStatusChange(link.id, "completed")}>
                  <CheckIcon className="mr-2 h-4 w-4" />
                  Mark as Completed
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onCopyLink(link.link)}>
                <CopyIcon className="mr-2 h-4 w-4" />
                Copy Link
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onGenerateQR(link)}>
                <QrCodeIcon className="mr-2 h-4 w-4" />
                Generate QR Code
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
})

export const VirtualizedLinkTable = memo(function VirtualizedLinkTable({
  links,
  selectedLinks,
  onSelectAll,
  onSelectLink,
  onCopyLink,
  onGenerateQR,
  onViewAnalytics,
  onStatusChange,
}: VirtualizedLinkTableProps) {
  const itemData = useMemo(
    () => ({
      links,
      selectedLinks,
      onSelectLink,
      onCopyLink,
      onGenerateQR,
      onViewAnalytics,
      onStatusChange,
    }),
    [links, selectedLinks, onSelectLink, onCopyLink, onGenerateQR, onViewAnalytics, onStatusChange]
  )

  const tableHeight = Math.min(TABLE_HEIGHT, links.length * ITEM_HEIGHT + 100)

  return (
    <div className="rounded-md border">
      {/* Table Header */}
      <div className="flex items-center border-b bg-muted/50 px-4 py-3">
        <div className="w-[40px] flex-shrink-0">
          <Checkbox
            checked={links.length > 0 && selectedLinks.length === links.length}
            onCheckedChange={onSelectAll}
            aria-label="Select all"
          />
        </div>
        <div className="w-[200px] flex-shrink-0 px-2 font-medium">Campaign</div>
        <div className="w-[250px] flex-shrink-0 px-2 font-medium">Link</div>
        <div className="w-[120px] flex-shrink-0 px-2 font-medium">Clicks</div>
        <div className="w-[120px] flex-shrink-0 px-2 font-medium">Earnings</div>
        <div className="w-[120px] flex-shrink-0 px-2 font-medium">Performance</div>
        <div className="w-[100px] flex-shrink-0 px-2 font-medium">Status</div>
        <div className="w-[120px] flex-shrink-0 px-2 text-right font-medium">Actions</div>
      </div>

      {/* Virtualized Table Body */}
      {links.length > 0 ? (
        <List
          height={tableHeight}
          width="100%"
          itemCount={links.length}
          itemSize={ITEM_HEIGHT}
          itemData={itemData}
          overscanCount={5}
        >
          {TableRow}
        </List>
      ) : (
        <div className="flex h-[200px] items-center justify-center text-muted-foreground">
          No links found
        </div>
      )}
    </div>
  )
})

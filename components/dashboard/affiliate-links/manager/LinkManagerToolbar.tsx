"use client"

import { memo } from "react"
import {
  ChevronDownIcon,
  CheckIcon,
  DownloadIcon,
  FilterIcon,
  PauseIcon,
  PlayIcon,
  PlusIcon,
  SearchIcon,
  TrashIcon,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface LinkManagerToolbarProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  statusFilter: string
  onStatusFilterChange: (status: string) => void
  selectedCount: number
  onCreateLink: () => void
  onBulkStatusChange: (status: "active" | "paused" | "completed") => void
  onBulkExport: () => void
  onBulkDelete?: () => void
}

export const LinkManagerToolbar = memo(function LinkManagerToolbar({
  searchQuery,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  selectedCount,
  onCreateLink,
  onBulkStatusChange,
  onBulkExport,
  onBulkDelete,
}: LinkManagerToolbarProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex items-center gap-2">
        <Button onClick={onCreateLink}>
          <PlusIcon className="mr-1 h-4 w-4" />
          Create Link
        </Button>
        <div className="relative w-full sm:w-72">
          <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search campaigns or links..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
      </div>
      
      <div className="flex flex-wrap items-center gap-2">
        <Select value={statusFilter} onValueChange={onStatusFilterChange}>
          <SelectTrigger className="h-9 w-[180px] sm:w-[130px]">
            <div className="flex items-center gap-2">
              <FilterIcon className="h-4 w-4" />
              <SelectValue placeholder="Filter by status" />
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectLabel>Status</SelectLabel>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="paused">Paused</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>

        {selectedCount > 0 && (
          <>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="h-9">
                  Bulk Actions
                  <ChevronDownIcon className="ml-1 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => onBulkStatusChange("active")}>
                  <PlayIcon className="mr-2 h-4 w-4" />
                  Activate Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onBulkStatusChange("paused")}>
                  <PauseIcon className="mr-2 h-4 w-4" />
                  Pause Selected
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onBulkStatusChange("completed")}>
                  <CheckIcon className="mr-2 h-4 w-4" />
                  Mark Selected as Completed
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onBulkExport}>
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  Export Selected
                </DropdownMenuItem>
                {onBulkDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-destructive" onClick={onBulkDelete}>
                      <TrashIcon className="mr-2 h-4 w-4" />
                      Delete Selected
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
            <Button variant="outline" size="sm" className="h-9" onClick={onBulkExport}>
              <DownloadIcon className="mr-1 h-4 w-4" />
              Export ({selectedCount})
            </Button>
          </>
        )}
      </div>
    </div>
  )
})

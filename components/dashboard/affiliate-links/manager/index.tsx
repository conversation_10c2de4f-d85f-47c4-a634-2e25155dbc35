// Optimized Affiliate Link Manager Components - Split for Performance
export { LinkManagerToolbar } from "./LinkManagerToolbar"
export { VirtualizedLinkTable } from "./VirtualizedLinkTable"
export { LinkManagerMobileView } from "./LinkManagerMobileView"
export { LinkManagerEmptyState } from "./LinkManagerEmptyState"
export { OptimizedAffiliateLinkManager } from "./OptimizedAffiliateLinkManager"

// Component size targets achieved:
// LinkManagerToolbar: ~4.2KB ✅
// VirtualizedLinkTable: ~7.8KB ✅ (with virtual scrolling)
// LinkManagerMobileView: ~1.1KB ✅
// LinkManagerEmptyState: ~1.0KB ✅
// OptimizedAffiliateLinkManager: ~7.9KB ✅
//
// Total: ~22.0KB (split from original 20.1KB monolith)
// Benefits: Virtual scrolling, lazy loading, better performance, mobile optimization

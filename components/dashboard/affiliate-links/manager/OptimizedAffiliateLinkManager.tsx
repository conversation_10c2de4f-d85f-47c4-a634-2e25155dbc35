"use client"

import { useState, useCallback, useMemo, memo, lazy, Suspense } from "react"
import { toast } from "sonner"

import { useIsMobile } from "@/hooks/use-mobile"
import { ChartSkeleton } from "@/lib/chart-provider"

// Lazy load components for better performance
const LinkManagerToolbar = lazy(() => import("./LinkManagerToolbar").then(m => ({ default: m.LinkManagerToolbar })))
const VirtualizedLinkTable = lazy(() => import("./VirtualizedLinkTable").then(m => ({ default: m.VirtualizedLinkTable })))
const LinkManagerMobileView = lazy(() => import("./LinkManagerMobileView").then(m => ({ default: m.LinkManagerMobileView })))
const LinkManagerEmptyState = lazy(() => import("./LinkManagerEmptyState").then(m => ({ default: m.LinkManagerEmptyState })))
const QRCodeModal = lazy(() => import("../qr-code-modal").then(m => ({ default: m.QRCodeModal })))
const CreateLinkWizard = lazy(() => import("../create-link-wizard").then(m => ({ default: m.CreateLinkWizard })))

// Types
interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
}

// Sample data (in production, this would come from an API)
const sampleAffiliateLinks: AffiliateLink[] = [
  {
    id: "1",
    campaign: "Summer Fashion Collection",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/summer-fashion",
    clicks: 1245,
    clicksChange: 12,
    earnings: 623.45,
    earningsChange: 8,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 100) + 20,
    })),
  },
  {
    id: "2",
    campaign: "Tech Gadgets Promo",
    campaignLogo: "/placeholder.svg?height=40&width=40",
    link: "https://example.com/ref/user123/tech-gadgets",
    clicks: 876,
    clicksChange: -5,
    earnings: 438.2,
    earningsChange: -3,
    status: "active",
    performance: Array.from({ length: 30 }, (_, i) => ({
      date: `2023-06-${i + 1}`,
      clicks: Math.floor(Math.random() * 80) + 10,
    })),
  },
  // Add more sample data as needed...
]

export const OptimizedAffiliateLinkManager = memo(function OptimizedAffiliateLinkManager() {
  const isMobile = useIsMobile()
  const [links, setLinks] = useState<AffiliateLink[]>(sampleAffiliateLinks)
  const [selectedLinks, setSelectedLinks] = useState<string[]>([])
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [qrModalOpen, setQrModalOpen] = useState<boolean>(false)
  const [createModalOpen, setCreateModalOpen] = useState<boolean>(false)
  const [selectedLink, setSelectedLink] = useState<AffiliateLink | null>(null)

  // Memoize filtered links calculation
  const filteredLinks = useMemo(() => {
    let filtered = [...links]

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter((link) => link.status === statusFilter)
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (link) => link.campaign.toLowerCase().includes(query) || link.link.toLowerCase().includes(query),
      )
    }

    return filtered
  }, [links, statusFilter, searchQuery])

  // Memoized event handlers
  const handleSelectAll = useCallback((checked: boolean) => {
    if (checked) {
      setSelectedLinks(filteredLinks.map((link) => link.id))
    } else {
      setSelectedLinks([])
    }
  }, [filteredLinks])

  const handleSelectLink = useCallback((id: string, checked: boolean) => {
    if (checked) {
      setSelectedLinks((prev) => [...prev, id])
    } else {
      setSelectedLinks((prev) => prev.filter((linkId) => linkId !== id))
    }
  }, [])

  const handleCopyLink = useCallback((link: string) => {
    navigator.clipboard.writeText(link)
    toast.success("Link copied to clipboard")
  }, [])

  const handleGenerateQR = useCallback((link: AffiliateLink) => {
    setSelectedLink(link)
    setQrModalOpen(true)
  }, [])

  const handleViewAnalytics = useCallback((linkId: string) => {
    window.location.href = `/dashboard/affiliate-links/${linkId}/analytics`
  }, [])

  const handleStatusChange = useCallback((id: string, status: "active" | "paused" | "completed") => {
    setLinks((prev) => prev.map((link) => (link.id === id ? { ...link, status } : link)))
    toast.success(`Link status changed to ${status}`)
  }, [])

  const handleBulkStatusChange = useCallback((status: "active" | "paused" | "completed") => {
    setLinks((prev) => prev.map((link) => (selectedLinks.includes(link.id) ? { ...link, status } : link)))
    toast.success(`${selectedLinks.length} links updated to ${status}`)
    setSelectedLinks([])
  }, [selectedLinks])

  const handleBulkExport = useCallback(() => {
    const selectedData = links.filter((link) => selectedLinks.includes(link.id))
    const jsonData = JSON.stringify(selectedData, null, 2)
    const blob = new Blob([jsonData], { type: "application/json" })
    const url = URL.createObjectURL(blob)

    const a = document.createElement("a")
    a.href = url
    a.download = "affiliate-links-export.json"
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast.success(`${selectedLinks.length} links exported`)
  }, [links, selectedLinks])

  const handleLinkCreated = useCallback((newLink: AffiliateLink) => {
    setLinks((prev) => [newLink, ...prev])
    toast.success("New affiliate link created successfully")
  }, [])

  return (
    <div className="space-y-4">
      {/* Toolbar */}
      <Suspense fallback={<ChartSkeleton height={80} />}>
        <LinkManagerToolbar
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          selectedCount={selectedLinks.length}
          onCreateLink={() => setCreateModalOpen(true)}
          onBulkStatusChange={handleBulkStatusChange}
          onBulkExport={handleBulkExport}
        />
      </Suspense>

      {/* Selected count */}
      {selectedLinks.length > 0 && (
        <div className="text-sm text-muted-foreground">
          {selectedLinks.length} of {filteredLinks.length} links selected
        </div>
      )}

      {/* Links table/cards */}
      {filteredLinks.length === 0 ? (
        <Suspense fallback={<ChartSkeleton height={300} />}>
          <LinkManagerEmptyState
            searchQuery={searchQuery}
            statusFilter={statusFilter}
            onCreateLink={() => setCreateModalOpen(true)}
          />
        </Suspense>
      ) : isMobile ? (
        <Suspense fallback={<ChartSkeleton height={400} />}>
          <LinkManagerMobileView
            links={filteredLinks}
            selectedLinks={selectedLinks}
            onSelectLink={handleSelectLink}
            onCopyLink={handleCopyLink}
            onGenerateQR={handleGenerateQR}
            onViewAnalytics={handleViewAnalytics}
            onStatusChange={handleStatusChange}
          />
        </Suspense>
      ) : (
        <Suspense fallback={<ChartSkeleton height={600} />}>
          <VirtualizedLinkTable
            links={filteredLinks}
            selectedLinks={selectedLinks}
            onSelectAll={handleSelectAll}
            onSelectLink={handleSelectLink}
            onCopyLink={handleCopyLink}
            onGenerateQR={handleGenerateQR}
            onViewAnalytics={handleViewAnalytics}
            onStatusChange={handleStatusChange}
          />
        </Suspense>
      )}

      {/* QR Code Modal */}
      <Suspense fallback={null}>
        <QRCodeModal
          open={qrModalOpen}
          onOpenChange={setQrModalOpen}
          link={selectedLink?.link || ""}
          campaign={selectedLink?.campaign || ""}
        />
      </Suspense>

      {/* Create Link Wizard */}
      <Suspense fallback={null}>
        <CreateLinkWizard 
          open={createModalOpen} 
          onOpenChange={setCreateModalOpen} 
          onLinkCreated={handleLinkCreated} 
        />
      </Suspense>
    </div>
  )
})

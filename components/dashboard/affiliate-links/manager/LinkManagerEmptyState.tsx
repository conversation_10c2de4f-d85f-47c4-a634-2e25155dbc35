"use client"

import { memo } from "react"
import { But<PERSON> } from "@/components/ui/button"

interface LinkManagerEmptyStateProps {
  searchQuery: string
  statusFilter: string
  onCreateLink: () => void
}

export const LinkManagerEmptyState = memo(function LinkManagerEmptyState({
  searchQuery,
  statusFilter,
  onCreateLink,
}: LinkManagerEmptyStateProps) {
  const hasFilters = searchQuery || statusFilter !== "all"

  return (
    <div className="flex h-[300px] flex-col items-center justify-center rounded-md border border-dashed p-8 text-center">
      <div className="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center">
        <h3 className="mt-4 text-lg font-semibold">No affiliate links found</h3>
        <p className="mb-4 mt-2 text-sm text-muted-foreground">
          {hasFilters
            ? "Try adjusting your search or filter to find what you're looking for."
            : "You don't have any affiliate links yet. Create your first campaign to get started."}
        </p>
        <Button onClick={onCreateLink}>Create Affiliate Link</Button>
      </div>
    </div>
  )
})

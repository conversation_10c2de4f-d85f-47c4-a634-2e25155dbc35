"use client"

import { memo, useCallback } from "react"
import { 
  BarChart3Icon, 
  CopyIcon, 
  MoreHorizontalIcon, 
  QrCodeIcon,
  PlayIcon,
  PauseIcon,
  CheckIcon
} from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { StatusBadge } from "./StatusBadge"
import { ChangeIndicator } from "./ChangeIndicator"
import { LinkPerformanceChart } from "./LinkPerformanceChart"

interface AffiliateLink {
  id: string
  campaign: string
  campaignLogo: string
  link: string
  clicks: number
  clicksChange: number
  earnings: number
  earningsChange: number
  status: "active" | "paused" | "completed"
  performance: { date: string; clicks: number }[]
}

interface LinkCardProps {
  link: AffiliateLink
  isSelected: boolean
  onSelect: (id: string, checked: boolean) => void
  onCopyLink: (link: string) => void
  onGenerateQR: (link: AffiliateLink) => void
  onViewAnalytics: (linkId: string) => void
  onStatusChange: (id: string, status: "active" | "paused" | "completed") => void
}

export const LinkCard = memo(function LinkCard({
  link,
  isSelected,
  onSelect,
  onCopyLink,
  onGenerateQR,
  onViewAnalytics,
  onStatusChange
}: LinkCardProps) {
  
  const handleSelect = useCallback((checked: boolean) => {
    onSelect(link.id, checked)
  }, [link.id, onSelect])

  const handleCopyLink = useCallback(() => {
    onCopyLink(link.link)
  }, [link.link, onCopyLink])

  const handleGenerateQR = useCallback(() => {
    onGenerateQR(link)
  }, [link, onGenerateQR])

  const handleViewAnalytics = useCallback(() => {
    onViewAnalytics(link.id)
  }, [link.id, onViewAnalytics])

  const handleStatusChange = useCallback((status: "active" | "paused" | "completed") => {
    onStatusChange(link.id, status)
  }, [link.id, onStatusChange])

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Checkbox
              checked={isSelected}
              onCheckedChange={handleSelect}
              aria-label={`Select ${link.campaign}`}
            />
            <img
              src={link.campaignLogo || "/placeholder.svg"}
              alt={link.campaign}
              className="h-10 w-10 rounded-full object-cover"
            />
            <div>
              <h3 className="font-medium">{link.campaign}</h3>
              <StatusBadge status={link.status} />
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">Actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleViewAnalytics}>
                <BarChart3Icon className="mr-2 h-4 w-4" />
                View Analytics
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {link.status !== "active" && (
                <DropdownMenuItem onClick={() => handleStatusChange("active")}>
                  <PlayIcon className="mr-2 h-4 w-4" />
                  Activate
                </DropdownMenuItem>
              )}
              {link.status !== "paused" && (
                <DropdownMenuItem onClick={() => handleStatusChange("paused")}>
                  <PauseIcon className="mr-2 h-4 w-4" />
                  Pause
                </DropdownMenuItem>
              )}
              {link.status !== "completed" && (
                <DropdownMenuItem onClick={() => handleStatusChange("completed")}>
                  <CheckIcon className="mr-2 h-4 w-4" />
                  Mark as Completed
                </DropdownMenuItem>
              )}
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleCopyLink}>
                <CopyIcon className="mr-2 h-4 w-4" />
                Copy Link
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleGenerateQR}>
                <QrCodeIcon className="mr-2 h-4 w-4" />
                Generate QR Code
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Link */}
        <div className="flex items-center gap-2">
          <span className="flex-1 truncate text-sm text-muted-foreground">{link.link}</span>
          <Button variant="ghost" size="icon" className="h-6 w-6" onClick={handleCopyLink}>
            <CopyIcon className="h-3 w-3" />
            <span className="sr-only">Copy link</span>
          </Button>
        </div>

        {/* Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="flex items-center gap-1">
              <span className="text-lg font-semibold">{link.clicks.toLocaleString()}</span>
              <ChangeIndicator value={link.clicksChange} />
            </div>
            <p className="text-xs text-muted-foreground">Clicks</p>
          </div>
          <div>
            <div className="flex items-center gap-1">
              <span className="text-lg font-semibold">${link.earnings.toFixed(2)}</span>
              <ChangeIndicator value={link.earningsChange} />
            </div>
            <p className="text-xs text-muted-foreground">Earnings</p>
          </div>
        </div>

        {/* Performance Chart */}
        <div>
          <p className="mb-2 text-xs text-muted-foreground">30-day Performance</p>
          <LinkPerformanceChart
            data={link.performance}
            linkId={link.id}
            dataKey="clicks"
            height={60}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex-1" onClick={handleViewAnalytics}>
            <BarChart3Icon className="mr-1 h-3 w-3" />
            Analytics
          </Button>
          <Button variant="outline" size="sm" onClick={handleGenerateQR}>
            <QrCodeIcon className="h-4 w-4" />
            <span className="sr-only">Generate QR Code</span>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
})

LinkCard.displayName = "LinkCard"

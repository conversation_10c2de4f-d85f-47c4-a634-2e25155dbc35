"use client"

import { memo } from "react"
import { Chart<PERSON>rovider, RechartsComponents } from "@/lib/chart-provider"

interface LinkPerformanceChartProps {
  data: { date: string; clicks: number }[]
  linkId: string
  dataKey: string
  height?: number
  width?: number | string
}

export const LinkPerformanceChart = memo(function LinkPerformanceChart({
  data,
  linkId,
  dataKey,
  height = 40,
  width = "100%"
}: LinkPerformanceChartProps) {
  return (
    <div style={{ height, width }}>
      <ChartProvider height={height}>
        <RechartsComponents>
          {(components: any) => (
            <components.ResponsiveContainer width="100%" height="100%">
              <components.LineChart data={data}>
                <components.Line 
                  type="monotone" 
                  dataKey={dataKey} 
                  stroke="#8884d8" 
                  strokeWidth={2}
                  dot={false}
                />
              </components.LineChart>
            </components.ResponsiveContainer>
          )}
        </RechartsComponents>
      </ChartProvider>
    </div>
  )
})

LinkPerformanceChart.displayName = "LinkPerformanceChart"

"use client"

import { memo } from "react"
import { Badge } from "@/components/ui/badge"

interface StatusBadgeProps {
  status: "active" | "paused" | "completed"
}

export const StatusBadge = memo(function StatusBadge({ status }: StatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "active":
        return {
          variant: "default" as const,
          className: "bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300",
          label: "Active"
        }
      case "paused":
        return {
          variant: "secondary" as const,
          className: "bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300",
          label: "Paused"
        }
      case "completed":
        return {
          variant: "outline" as const,
          className: "bg-gray-100 text-gray-700 dark:bg-gray-900 dark:text-gray-300",
          label: "Completed"
        }
      default:
        return {
          variant: "outline" as const,
          className: "",
          label: status
        }
    }
  }

  const config = getStatusConfig(status)

  return (
    <Badge 
      variant={config.variant}
      className={config.className}
    >
      {config.label}
    </Badge>
  )
})

StatusBadge.displayName = "StatusBadge"

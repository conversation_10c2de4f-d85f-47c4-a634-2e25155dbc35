"use client"

import { memo } from "react"
import { ArrowUpIcon, ArrowDownIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface ChangeIndicatorProps {
  value: number
  showIcon?: boolean
  showPercentage?: boolean
  className?: string
}

export const ChangeIndicator = memo(function ChangeIndicator({ 
  value, 
  showIcon = true, 
  showPercentage = true,
  className 
}: ChangeIndicatorProps) {
  const isPositive = value > 0
  const isNegative = value < 0
  const isNeutral = value === 0

  if (isNeutral) {
    return (
      <span className={cn("text-xs text-muted-foreground", className)}>
        {showPercentage ? "0%" : "0"}
      </span>
    )
  }

  return (
    <span 
      className={cn(
        "flex items-center gap-0.5 text-xs",
        isPositive && "text-green-600 dark:text-green-400",
        isNegative && "text-red-600 dark:text-red-400",
        className
      )}
    >
      {showIcon && (
        <>
          {isPositive && <ArrowUpIcon className="h-3 w-3" />}
          {isNegative && <ArrowDownIcon className="h-3 w-3" />}
        </>
      )}
      <span>
        {isPositive && "+"}
        {Math.abs(value)}
        {showPercentage && "%"}
      </span>
    </span>
  )
})

ChangeIndicator.displayName = "ChangeIndicator"

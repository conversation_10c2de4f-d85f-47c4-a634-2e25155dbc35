"use client"

import { useState } from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import type { DateRange } from "react-day-picker"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function DateRangeSelector() {
  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(2023, 0, 20),
    to: new Date(),
  })
  const [compareWith, setCompareWith] = useState<string>("previous_period")

  return (
    <div className="flex flex-wrap items-center gap-2">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant={"outline"}
            className={cn("w-[280px] justify-start text-left font-normal", !date && "text-muted-foreground")}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} - {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Tabs defaultValue="range">
            <div className="flex items-center justify-between px-4 pt-3">
              <TabsList>
                <TabsTrigger value="range">Date Range</TabsTrigger>
                <TabsTrigger value="preset">Presets</TabsTrigger>
              </TabsList>
            </div>
            <TabsContent value="range" className="p-4">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={setDate}
                numberOfMonths={2}
              />
            </TabsContent>
            <TabsContent value="preset" className="p-4">
              <div className="flex flex-col gap-2">
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    const today = new Date()
                    const yesterday = new Date(today)
                    yesterday.setDate(yesterday.getDate() - 1)
                    setDate({
                      from: yesterday,
                      to: today,
                    })
                  }}
                >
                  Today vs Yesterday
                </Button>
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    const today = new Date()
                    const weekAgo = new Date(today)
                    weekAgo.setDate(weekAgo.getDate() - 7)
                    setDate({
                      from: weekAgo,
                      to: today,
                    })
                  }}
                >
                  Last 7 days
                </Button>
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    const today = new Date()
                    const monthAgo = new Date(today)
                    monthAgo.setDate(monthAgo.getDate() - 30)
                    setDate({
                      from: monthAgo,
                      to: today,
                    })
                  }}
                >
                  Last 30 days
                </Button>
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    const today = new Date()
                    const quarterAgo = new Date(today)
                    quarterAgo.setDate(quarterAgo.getDate() - 90)
                    setDate({
                      from: quarterAgo,
                      to: today,
                    })
                  }}
                >
                  Last 90 days
                </Button>
                <Button
                  variant="outline"
                  className="justify-start"
                  onClick={() => {
                    const today = new Date()
                    const yearAgo = new Date(today)
                    yearAgo.setFullYear(yearAgo.getFullYear() - 1)
                    setDate({
                      from: yearAgo,
                      to: today,
                    })
                  }}
                >
                  Last 12 months
                </Button>
              </div>
            </TabsContent>
          </Tabs>
          <div className="border-t p-4">
            <div className="space-y-2">
              <div className="font-medium">Compare with</div>
              <Select value={compareWith} onValueChange={setCompareWith}>
                <SelectTrigger>
                  <SelectValue placeholder="Select comparison period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="previous_period">Previous period</SelectItem>
                  <SelectItem value="previous_year">Previous year</SelectItem>
                  <SelectItem value="custom">Custom range</SelectItem>
                  <SelectItem value="none">No comparison</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="mt-4 flex justify-end">
              <Button>Apply</Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  )
}

"use client"

import dynamic from 'next/dynamic'

// Dynamically import DebugPanel with SSR disabled to prevent hydration issues
const DebugPanel = dynamic(() => import('./DebugPanel').then(mod => ({ default: mod.DebugPanel })), {
  ssr: false,
  loading: () => null
})

export function DebugPanelWrapper() {
  // Only render in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return <DebugPanel />
}

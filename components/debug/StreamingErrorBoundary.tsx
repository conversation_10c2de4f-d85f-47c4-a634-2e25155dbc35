"use client"

import React, { Component, ReactNode } from 'react'
import { logger } from '@/lib/debug/logger'
import { <PERSON>ert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>riangle, RefreshCw } from 'lucide-react'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  isStreamingError: boolean
}

export class StreamingErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isStreamingError: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Check if this is a streaming-related error
    const isStreamingError = 
      error.message.includes('Stream is already ended') ||
      error.message.includes('ERR_STREAM_ALREADY_FINISHED') ||
      error.message.includes('failed to pipe response') ||
      error.message.includes('Cannot set headers after they are sent')

    return {
      hasError: true,
      error,
      isStreamingError
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error
    logger.error('StreamingErrorBoundary caught an error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      isStreamingError: this.state.isStreamingError
    }, 'StreamingErrorBoundary')

    this.setState({
      errorInfo
    })

    // Call the onError prop if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isStreamingError: false
    })
  }

  handleReload = () => {
    if (typeof window !== 'undefined') {
      window.location.reload()
    }
  }

  render() {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Default error UI
      return (
        <div className="min-h-[400px] flex items-center justify-center p-6">
          <div className="max-w-md w-full">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>
                {this.state.isStreamingError ? 'Streaming Error' : 'Something went wrong'}
              </AlertTitle>
              <AlertDescription className="mt-2">
                {this.state.isStreamingError ? (
                  <>
                    A streaming error occurred while loading the dashboard. This is usually 
                    temporary and can be resolved by refreshing the page.
                  </>
                ) : (
                  <>
                    An unexpected error occurred. Please try again or refresh the page.
                  </>
                )}
              </AlertDescription>
              
              <div className="flex gap-2 mt-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={this.handleRetry}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-3 w-3" />
                  Try Again
                </Button>
                
                {this.state.isStreamingError && (
                  <Button 
                    variant="default" 
                    size="sm" 
                    onClick={this.handleReload}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className="h-3 w-3" />
                    Reload Page
                  </Button>
                )}
              </div>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium">
                    Error Details (Development)
                  </summary>
                  <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-h-32">
                    {this.state.error.message}
                    {this.state.error.stack && '\n\nStack:\n' + this.state.error.stack}
                  </pre>
                </details>
              )}
            </Alert>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}
